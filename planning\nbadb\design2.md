
### Query Example 1

* **Use Case / Analytical Question:** Retrieve a specific player's career regular season totals and averages across all seasons they played.
* **Goal of this Query:** To aggregate and display key statistical totals (points, rebounds, assists, etc.) and per-game averages for a single player's entire regular season career.
* **Key Tables Involved & Joins:**
  * `Players` (p): To find the `player_id` by name and get player information.
  * `PlayerSeasonTotals` (pst): Contains aggregated season stats for players.
  * `Seasons` (s): To filter by `season_type` = 'Regular Season' and potentially display season years.
  * Joins: `Players` -> `PlayerSeasonTotals` -> `Seasons` (though `Seasons` might not be strictly needed if `PlayerSeasonTotals` already has `season_type` and `season_year`). Assuming `PlayerSeasonTotals` has `season_type`.
* **Example SQL Query:**

    ```sql
    -- Use Case: Retrieve a specific player's career regular season totals and averages.
    -- Player: <PERSON><PERSON><PERSON> James
    
    WITH PlayerInfo AS (
        SELECT player_id, first_name, last_name
        FROM Players
        WHERE first_name = '<PERSON><PERSON><PERSON>' AND last_name = '<PERSON>' -- Adjust player name as needed
        LIMIT 1 -- Ensure only one player if names can be ambiguous (though player_id is better)
    ),
    PlayerCareerRegularSeasonStats AS (
        SELECT
            pst.player_id,
            COUNT(DISTINCT pst.season_id) AS seasons_played,
            SUM(pst.games_played) AS total_games_played,
            SUM(pst.minutes_played_total) AS total_minutes,
            SUM(pst.points_total) AS total_points,
            SUM(pst.rebounds_total) AS total_rebounds,
            SUM(pst.assists_total) AS total_assists,
            SUM(pst.steals_total) AS total_steals,
            SUM(pst.blocks_total) AS total_blocks,
            SUM(pst.field_goals_made_total) AS total_fgm,
            SUM(pst.field_goals_attempted_total) AS total_fga,
            SUM(pst.three_pointers_made_total) AS total_3pm,
            SUM(pst.three_pointers_attempted_total) AS total_3pa,
            SUM(pst.free_throws_made_total) AS total_ftm,
            SUM(pst.free_throws_attempted_total) AS total_fta
        FROM
            PlayerSeasonTotals AS pst
        JOIN
            PlayerInfo AS pi ON pst.player_id = pi.player_id
        WHERE
            pst.season_type = 'Regular Season' -- Assuming 'season_type' column exists
        GROUP BY
            pst.player_id
    )
    SELECT
        pi.first_name,
        pi.last_name,
        pcrss.seasons_played,
        pcrss.total_games_played,
        pcrss.total_minutes,
        pcrss.total_points,
        pcrss.total_rebounds,
        pcrss.total_assists,
        pcrss.total_steals,
        pcrss.total_blocks,
        ROUND(pcrss.total_points * 1.0 / NULLIF(pcrss.total_games_played, 0), 1) AS ppg,
        ROUND(pcrss.total_rebounds * 1.0 / NULLIF(pcrss.total_games_played, 0), 1) AS rpg,
        ROUND(pcrss.total_assists * 1.0 / NULLIF(pcrss.total_games_played, 0), 1) AS apg,
        ROUND(pcrss.total_steals * 1.0 / NULLIF(pcrss.total_games_played, 0), 1) AS spg,
        ROUND(pcrss.total_blocks * 1.0 / NULLIF(pcrss.total_games_played, 0), 1) AS bpg,
        ROUND(pcrss.total_fgm * 100.0 / NULLIF(pcrss.total_fga, 0), 1) AS fg_percentage,
        ROUND(pcrss.total_3pm * 100.0 / NULLIF(pcrss.total_3pa, 0), 1) AS three_p_percentage,
        ROUND(pcrss.total_ftm * 100.0 / NULLIF(pcrss.total_fta, 0), 1) AS ft_percentage
    FROM
        PlayerInfo AS pi
    JOIN
        PlayerCareerRegularSeasonStats AS pcrss ON pi.player_id = pcrss.player_id;
    ```*   **Explanation of Query:**
    1.  **`PlayerInfo` CTE:** Selects the `player_id` for the specified player (e.g., 'LeBron James'). Using `player_id` directly is more robust if known.
    2.  **`PlayerCareerRegularSeasonStats` CTE:**
        *   Joins `PlayerSeasonTotals` with `PlayerInfo` on `player_id`.
        *   Filters for `season_type = 'Regular Season'`.
        *   Groups by `player_id` to aggregate stats across all their regular seasons.
        *   Uses `SUM()` to calculate career totals for various statistical categories.
        *   Counts distinct seasons played.
    3.  **Final `SELECT` Statement:**
        *   Joins `PlayerInfo` with `PlayerCareerRegularSeasonStats`.
        *   Selects player name, career totals.
        *   Calculates per-game averages (PPG, RPG, APG, SPG, BPG) by dividing totals by `total_games_played`. `NULLIF(total_games_played, 0)` prevents division by zero errors. `* 1.0` ensures floating-point division.
        *   Calculates shooting percentages (FG%, 3P%, FT%).
        *   `ROUND()` is used to format averages and percentages to one decimal place.
* **Expected Output Columns (Conceptual):** `first_name`, `last_name`, `seasons_played`, `total_games_played`, `total_minutes`, `total_points`, `total_rebounds`, `total_assists`, `total_steals`, `total_blocks`, `ppg`, `rpg`, `apg`, `spg`, `bpg`, `fg_percentage`, `three_p_percentage`, `ft_percentage`.

---

### Query Example 2

* **Use Case / Analytical Question:** List all MVP award winners and their key stats for the winning season.
* **Goal of this Query:** To identify all players who won the regular season MVP award, the season they won it, and their primary statistics (points, rebounds, assists per game) for that specific season.
* **Key Tables Involved & Joins:**
  * `Awards` (a): To identify the MVP award (`award_name = 'Most Valuable Player'`).
  * `PlayerAwards` (pa): Links players to awards for specific seasons.
  * `Players` (p): To get player names.
  * `PlayerSeasonTotals` (pst): To get the player's stats for the MVP-winning season.
  * `Seasons` (s): To get the `season_year` for display.
  * Joins: `Awards` -> `PlayerAwards` -> `Players`, `PlayerAwards` -> `PlayerSeasonTotals` (on `player_id` and `season_id`), `PlayerAwards` -> `Seasons`.
* **Example SQL Query:**

    ```sql
    -- Use Case: List all MVP award winners and their key stats for the winning season.
    
    SELECT
        s.season_identifier, -- e.g., '2022-23' or '2023' depending on your Seasons table
        p.first_name,
        p.last_name,
        t.team_abbreviation AS team, -- Assuming PlayerSeasonTotals has team_id, and Teams table for abbreviation
        pst.games_played,
        pst.points_per_game,
        pst.rebounds_per_game,
        pst.assists_per_game,
        pst.field_goal_percentage,
        pst.three_point_percentage
    FROM
        Awards AS a
    JOIN
        PlayerAwards AS pa ON a.award_id = pa.award_id
    JOIN
        Players AS p ON pa.player_id = p.player_id
    JOIN
        Seasons AS s ON pa.season_id = s.season_id -- Or directly from PlayerAwards if season_year is there
    JOIN
        PlayerSeasonTotals AS pst ON pa.player_id = pst.player_id AND pa.season_id = pst.season_id AND pst.season_type = 'Regular Season'
    LEFT JOIN -- Use LEFT JOIN in case team info is sparse for very old awards
        Teams AS t ON pst.team_id = t.team_id -- Assuming PlayerSeasonTotals has team_id
    WHERE
        a.award_name = 'Most Valuable Player' -- Or the specific name used in your Awards table
        AND a.league_id IN (SELECT league_id FROM Leagues WHERE league_abbreviation = 'NBA') -- Filter for NBA MVPs
    ORDER BY
        s.season_identifier DESC;
    ```

* **Explanation of Query:**
    1. Selects season identifier, player name, team, and key per-game stats.
    2. Starts from the `Awards` table and filters for the 'Most Valuable Player' award and for the 'NBA' league.
    3. Joins `PlayerAwards` to link the award to a specific player and season.
    4. Joins `Players` to get the player's name.
    5. Joins `Seasons` to get the season identifier (e.g., "2022-23").
    6. Joins `PlayerSeasonTotals` on both `player_id` and `season_id` (and ensuring it's 'Regular Season' stats) to retrieve the player's statistics for that specific MVP season.
    7. Optionally joins `Teams` to get the player's team abbreviation for that season (assuming `PlayerSeasonTotals` stores `team_id`).
    8. Orders the results by season in descending order to show recent MVPs first.
* **Expected Output Columns (Conceptual):** `season_identifier`, `first_name`, `last_name`, `team`, `games_played`, `points_per_game`, `rebounds_per_game`, `assists_per_game`, `field_goal_percentage`, `three_point_percentage`.

---

### Query Example 3

* **Use Case / Analytical Question:** Find all games where a player scored over 50 points.
* **Goal of this Query:** To list all individual game instances where a specific player (or any player) exceeded 50 points, showing game details and their full stat line for that game.
* **Key Tables Involved & Joins:**
  * `Players` (p): To filter by player name (if specific player) or get names.
  * `PlayerGameStats` (pgs): Contains player statistics for individual games, including points.
  * `Games` (g): Contains game details like date, opponent.
  * `Teams` (ht, at): To get home and away team names/abbreviations.
  * Joins: `PlayerGameStats` -> `Players`, `PlayerGameStats` -> `Games`, `Games` -> `Teams` (aliased for home and away).
* **Example SQL Query:**

    ```sql
    -- Use Case: Find all games where a player scored 50+ points.
    -- Example for a specific player (Michael Jordan), can be adapted for any player.
    
    SELECT
        g.game_date,
        p.first_name || ' ' || p.last_name AS player_name,
        home_team.team_abbreviation AS home_team,
        away_team.team_abbreviation AS away_team,
        CASE 
            WHEN pgs.team_id = g.home_team_id THEN 'vs ' || away_team.team_abbreviation
            ELSE '@ ' || home_team.team_abbreviation
        END AS opponent_display,
        pgs.minutes_played,
        pgs.points_scored,
        pgs.rebounds_total,
        pgs.assists_total,
        pgs.steals_total,
        pgs.blocks_total,
        pgs.field_goals_made || '-' || pgs.field_goals_attempted AS fgm_fga,
        pgs.three_pointers_made || '-' || pgs.three_pointers_attempted AS tpm_tpa,
        pgs.free_throws_made || '-' || pgs.free_throws_attempted AS ftm_fta
    FROM
        PlayerGameStats AS pgs
    JOIN
        Players AS p ON pgs.player_id = p.player_id
    JOIN
        Games AS g ON pgs.game_id = g.game_id
    JOIN
        Teams AS home_team ON g.home_team_id = home_team.team_id
    JOIN
        Teams AS away_team ON g.away_team_id = away_team.team_id
    WHERE
        pgs.points_scored > 50
        -- AND p.first_name = 'Michael' AND p.last_name = 'Jordan' -- Uncomment to filter for a specific player
        AND g.season_type = 'Regular Season' -- Or 'Playoffs', or remove for all game types
    ORDER BY
        pgs.points_scored DESC,
        g.game_date DESC;
    ```

* **Explanation of Query:**
    1. Selects game date, player name, team details, opponent, and the player's full stat line for the game.
    2. Starts with `PlayerGameStats` as it contains the `points_scored`.
    3. Joins `Players` to get the player's name.
    4. Joins `Games` to get game details like date and team IDs.
    5. Joins `Teams` twice (aliased as `home_team` and `away_team`) to get the names/abbreviations of the teams involved.
    6. The `CASE` statement creates a user-friendly `opponent_display` string (e.g., "vs LAL" or "@ BOS").
    7. The `WHERE` clause filters for games where `points_scored > 50`.
    8. An optional filter for a specific player name is commented out.
    9. Filters for `Regular Season` games (can be adjusted).
    10. Orders results by points scored (descending) and then by game date (descending).
* **Expected Output Columns (Conceptual):** `game_date`, `player_name`, `home_team`, `away_team`, `opponent_display`, `minutes_played`, `points_scored`, `rebounds_total`, `assists_total`, `steals_total`, `blocks_total`, `fgm_fga`, `tpm_tpa`, `ftm_fta`.

---

### Query Example 4

* **Use Case / Analytical Question:** Get the season-by-season regular season record (Wins, Losses) for a specific franchise.
* **Goal of this Query:** To display the win-loss record for each regular season for a given franchise, showing how their performance has varied over time.
* **Key Tables Involved & Joins:**
  * `Franchises` (f): To identify the franchise by name.
  * `Teams` (t): To link franchise to specific team instances over seasons (as team names/IDs might change under one franchise).
  * `TeamSeason` (ts): Contains team's seasonal aggregate data, including wins and losses. (Assuming this table exists as per your schema notes: "Teams (Basic, Seasonal, Personnel)"). If not, this might be derived from `TeamGameStats` or `Games`. For this example, I'll assume `TeamSeason` exists.
  * `Seasons` (s): To get the season identifier and filter for regular season.
  * Joins: `Franchises` -> `Teams` (if `Teams` table links to `franchise_id`) -> `TeamSeason` -> `Seasons`. *Correction: A more direct `TeamSeason` table linked to `team_id` and `season_id` is common. `Teams` would hold franchise affiliation.*
* **Example SQL Query:**

    ```sql
    -- Use Case: Get the season-by-season regular season record for a specific franchise.
    -- Franchise: Los Angeles Lakers
    
    SELECT
        s.season_identifier, -- e.g., '2022-23'
        t.team_name AS team_name_that_season, -- The name the team used that season
        ts.wins,
        ts.losses,
        ts.win_percentage, -- Assuming this is pre-calculated or can be calculated
        ts.playoff_result -- e.g., 'Lost NBA Finals', 'Won Championship', 'First Round' (if available)
    FROM
        Franchises AS f
    JOIN
        Teams AS t ON f.franchise_id = t.franchise_id -- Links all team instances to the franchise
    JOIN
        TeamSeason AS ts ON t.team_id = ts.team_id -- Team's record for a specific season
    JOIN
        Seasons AS s ON ts.season_id = s.season_id
    WHERE
        f.franchise_name = 'Los Angeles Lakers' -- Adjust franchise name as needed
        AND s.season_type = 'Regular Season' -- Ensure we are looking at regular season records
                                          -- TeamSeason might already be specific to regular season.
    ORDER BY
        s.season_identifier ASC;
    ```

* **Explanation of Query:**
    1. Selects season identifier, the team name used that season, wins, losses, win percentage, and playoff result.
    2. Starts with `Franchises` to filter for the desired franchise (e.g., 'Los Angeles Lakers').
    3. Joins `Teams` to get all team instances (different `team_id`s or names like 'Minneapolis Lakers', 'Los Angeles Lakers') that belong to this franchise.
    4. Joins `TeamSeason` which is assumed to hold the W-L record for each team in each season.
    5. Joins `Seasons` to get the `season_identifier` and to filter for 'Regular Season'.
    6. Orders results by season chronologically.
* **Expected Output Columns (Conceptual):** `season_identifier`, `team_name_that_season`, `wins`, `losses`, `win_percentage`, `playoff_result`.
    *(Note: If `TeamSeason` doesn't exist, wins/losses would need to be calculated by counting game outcomes from the `Games` or `TeamGameStats` table, grouped by team and season.)*

---

### Query Example 5

* **Use Case / Analytical Question:** Compare head-to-head game results between two teams in a given season.
* **Goal of this Query:** To list all games played between two specific teams during a particular season, showing the date, home/away teams, and the final score.
* **Key Tables Involved & Joins:**
  * `Games` (g): Central table with game details, including home/away team IDs and scores.
  * `Teams` (t1, t2, ht, at): To identify the two specific teams by name/abbreviation and to display their names.
  * `Seasons` (s): To filter for a specific season.
  * Joins: `Games` -> `Teams` (multiple times for home, away, and the two target teams), `Games` -> `Seasons`.
* **Example SQL Query:**

    ```sql
    -- Use Case: Compare head-to-head game results between two teams in a given season.
    -- Teams: Boston Celtics vs. Los Angeles Lakers
    -- Season: 2022-23 (represented as season_id or by year in Seasons table)
    
    WITH TargetTeams AS (
        SELECT team_id, team_abbreviation AS name FROM Teams WHERE team_abbreviation = 'BOS' -- Team 1 Abbreviation
        UNION ALL
        SELECT team_id, team_abbreviation AS name FROM Teams WHERE team_abbreviation = 'LAL' -- Team 2 Abbreviation
    ),
    TargetSeason AS (
        SELECT season_id FROM Seasons WHERE season_identifier = '2022-23' -- Adjust season identifier
    )
    SELECT
        g.game_date,
        s.season_identifier,
        ht.team_abbreviation AS home_team,
        g.home_team_score,
        at.team_abbreviation AS away_team,
        g.away_team_score,
        CASE
            WHEN g.home_team_score > g.away_team_score THEN ht.team_abbreviation
            WHEN g.away_team_score > g.home_team_score THEN at.team_abbreviation
            ELSE 'Tie' -- Should not happen in NBA post-shot clock era for regular games
        END AS winning_team_abbreviation
    FROM
        Games AS g
    JOIN
        Teams AS ht ON g.home_team_id = ht.team_id
    JOIN
        Teams AS at ON g.away_team_id = at.team_id
    JOIN
        Seasons AS s ON g.season_id = s.season_id
    WHERE
        g.season_id = (SELECT season_id FROM TargetSeason)
        AND (
                (g.home_team_id = (SELECT team_id FROM TargetTeams WHERE name = 'BOS') AND g.away_team_id = (SELECT team_id FROM TargetTeams WHERE name = 'LAL'))
             OR (g.home_team_id = (SELECT team_id FROM TargetTeams WHERE name = 'LAL') AND g.away_team_id = (SELECT team_id FROM TargetTeams WHERE name = 'BOS'))
            )
        -- AND g.season_type = 'Regular Season' -- Uncomment to specify game type
    ORDER BY
        g.game_date ASC;
    ```

* **Explanation of Query:**
    1. **`TargetTeams` CTE:** Selects the `team_id`s for the two teams of interest (e.g., 'BOS' and 'LAL').
    2. **`TargetSeason` CTE:** Selects the `season_id` for the season of interest.
    3. The main `SELECT` statement retrieves game date, season, home/away teams and their scores, and determines the winner.
    4. Joins `Games` with `Teams` (for home and away team names) and `Seasons`.
    5. The `WHERE` clause is crucial:
        * It filters for the `season_id` from `TargetSeason`.
        * It then checks if the game's home/away team IDs match the pair of `team_id`s from `TargetTeams` in either order.
    6. An optional filter for `season_type` is included.
    7. Orders results by game date.
* **Expected Output Columns (Conceptual):** `game_date`, `season_identifier`, `home_team`, `home_team_score`, `away_team`, `away_team_score`, `winning_team_abbreviation`.

---

### Query Example 6

* **Use Case / Analytical Question:** List players drafted in a specific year with their draft position and eventual NBA stats (career PPG, RPG, APG if available).
* **Goal of this Query:** To show who was drafted in a particular year, their draft details (pick number, drafting team), and a summary of their subsequent NBA career performance.
* **Key Tables Involved & Joins:**
  * `Drafts` (d): Contains information about each draft year, linking to `season_id`.
  * `DraftPicks` (dp): Details for each pick (player, pick number, team).
  * `Players` (p): To get player names.
  * `Teams` (t): To get the name of the team that drafted the player.
  * `PlayerSeasonTotals` (pst): To aggregate career stats (this requires a subquery or CTE to calculate career averages).
  * Joins: `Drafts` -> `DraftPicks` -> `Players`, `DraftPicks` -> `Teams`. Then `Players` -> `PlayerSeasonTotals` (for career stats).
* **Example SQL Query:**

    ```sql
    -- Use Case: List players drafted in a specific year with draft position and career NBA stats.
    -- Draft Year: 2003 (This would correspond to a season_id, e.g., for the season leading up to the draft)
    
    WITH DraftYearPlayers AS (
        SELECT
            d.draft_year, -- Assuming Drafts table has a draft_year column
            dp.overall_pick_number,
            dp.round_number,
            dp.round_pick_number,
            p.player_id,
            p.first_name,
            p.last_name,
            t_draft.team_name AS drafting_team
        FROM
            Drafts AS d
        JOIN
            DraftPicks AS dp ON d.draft_id = dp.draft_id
        JOIN
            Players AS p ON dp.player_id = p.player_id
        JOIN
            Teams AS t_draft ON dp.team_id = t_draft.team_id
        WHERE
            d.draft_year = 2003 -- Specify the draft year
    ),
    PlayerCareerStatsSummary AS (
        -- This CTE calculates career regular season averages for all players
        -- It could be pre-calculated in another table or view for performance if frequently needed
        SELECT
            pst.player_id,
            COUNT(DISTINCT pst.season_id) AS seasons_played_in_nba,
            SUM(pst.games_played) AS total_games_played_nba,
            ROUND(SUM(pst.points_total) * 1.0 / NULLIF(SUM(pst.games_played), 0), 1) AS career_ppg,
            ROUND(SUM(pst.rebounds_total) * 1.0 / NULLIF(SUM(pst.games_played), 0), 1) AS career_rpg,
            ROUND(SUM(pst.assists_total) * 1.0 / NULLIF(SUM(pst.games_played), 0), 1) AS career_apg
        FROM
            PlayerSeasonTotals AS pst
        JOIN
            Seasons AS s ON pst.season_id = s.season_id
        JOIN
            Leagues AS l ON s.league_id = l.league_id
        WHERE
            pst.season_type = 'Regular Season'
            AND l.league_abbreviation = 'NBA' -- Ensure stats are from NBA
        GROUP BY
            pst.player_id
        HAVING SUM(pst.games_played) > 0 -- Only players who actually played games
    )
    SELECT
        dyp.draft_year,
        dyp.overall_pick_number,
        dyp.first_name,
        dyp.last_name,
        dyp.drafting_team,
        COALESCE(pcs.seasons_played_in_nba, 0) AS seasons_in_nba,
        COALESCE(pcs.total_games_played_nba, 0) AS games_in_nba,
        pcs.career_ppg,
        pcs.career_rpg,
        pcs.career_apg
    FROM
        DraftYearPlayers AS dyp
    LEFT JOIN
        PlayerCareerStatsSummary AS pcs ON dyp.player_id = pcs.player_id
    ORDER BY
        dyp.overall_pick_number ASC;
    ```

* **Explanation of Query:**
    1. **`DraftYearPlayers` CTE:** Selects all players drafted in a specific year (`draft_year = 2003`), along with their draft position details and drafting team.
    2. **`PlayerCareerStatsSummary` CTE:** This is a more complex CTE that calculates career regular season PPG, RPG, and APG for *all* players who played in the NBA. It aggregates data from `PlayerSeasonTotals`, ensuring it's for 'Regular Season' and 'NBA' league.
    3. **Final `SELECT` Statement:**
        * Joins `DraftYearPlayers` with `PlayerCareerStatsSummary` using a `LEFT JOIN` (because a drafted player might never have played in the NBA, so we still want to list them).
        * Selects draft information and the summarized career stats.
        * `COALESCE` is used to show 0 for seasons/games played if the player has no NBA stats.
        * Orders the results by the overall pick number.
* **Expected Output Columns (Conceptual):** `draft_year`, `overall_pick_number`, `first_name`, `last_name`, `drafting_team`, `seasons_in_nba`, `games_in_nba`, `career_ppg`, `career_rpg`, `career_apg`.

---

### Query Example 7

* **Use Case / Analytical Question:** Identify all transactions a specific player was involved in.
* **Goal of this Query:** To list all trades, signings, waivers, etc., that a particular player was part of, showing the date, transaction type, and other teams/players involved if applicable.
* **Key Tables Involved & Joins:**
  * `Players` (p): To find the player by name.
  * `TransactionPlayersInvolved` (tpi): Links players to specific transactions.
  * `Transactions` (tr): Contains details about each transaction (date, type, description/notes).
  * Potentially `Teams` (t_from, t_to): If transactions involve movement between teams and these are stored as IDs in `TransactionPlayersInvolved` or `Transactions`.
  * Joins: `Players` -> `TransactionPlayersInvolved` -> `Transactions`.
* **Example SQL Query:**

    ```sql
    -- Use Case: Identify all transactions a specific player was involved in.
    -- Player: Kevin Durant
    
    SELECT
        tr.transaction_date,
        tr.transaction_type, -- e.g., 'Trade', 'Signed Free Agent', 'Waived'
        p.first_name || ' ' || p.last_name AS player_involved_main,
        tpi.status_description, -- e.g., 'Acquired by Team X', 'Traded from Team Y', 'Signed contract'
        tr.notes AS transaction_details, -- Full description of the transaction
        -- The following columns attempt to show other players/teams if the transaction was a trade
        -- This part heavily depends on how your 'Transactions' and 'TransactionPlayersInvolved' tables are structured
        -- to represent multi-player/multi-team trades.
        -- For simplicity, this example assumes 'notes' has a good textual description.
        -- A more complex query might be needed to list all *other* players involved.
        (SELECT STRING_AGG(pl_other.first_name || ' ' || pl_other.last_name, ', ')
         FROM TransactionPlayersInvolved AS tpi_other
         JOIN Players AS pl_other ON tpi_other.player_id = pl_other.player_id
         WHERE tpi_other.transaction_id = tr.transaction_id AND tpi_other.player_id != p.player_id
        ) AS other_players_in_transaction
    FROM
        Players AS p
    JOIN
        TransactionPlayersInvolved AS tpi ON p.player_id = tpi.player_id
    JOIN
        Transactions AS tr ON tpi.transaction_id = tr.transaction_id
    WHERE
        p.first_name = 'Kevin' AND p.last_name = 'Durant' -- Adjust player name
    ORDER BY
        tr.transaction_date DESC;
    ```*   **Explanation of Query:**
    1.  Selects transaction date, type, the main player's name, their status in the transaction, and overall transaction notes.
    2.  Starts from `Players` to filter for the specific player.
    3.  Joins `TransactionPlayersInvolved` to find all transactions linked to this player.
    4.  Joins `Transactions` to get the details of each transaction.
    5.  The subquery `(SELECT STRING_AGG(...) ...)` is an attempt to list other players involved in the same transaction. `STRING_AGG` concatenates names into a comma-separated list. This part is complex and highly dependent on your schema for representing multi-party trades. If `Transactions.notes` already contains a good textual summary, that might be sufficient for many use cases.
    6.  Orders transactions by date, most recent first.
* **Expected Output Columns (Conceptual):** `transaction_date`, `transaction_type`, `player_involved_main`, `status_description`, `transaction_details`, `other_players_in_transaction`.

---

These examples should give you a good starting point for exploring your database. The complexity of some queries (like detailed trade breakdowns or precise historical win/loss calculations if not pre-aggregated) highlights the importance of a well-thought-out schema.
