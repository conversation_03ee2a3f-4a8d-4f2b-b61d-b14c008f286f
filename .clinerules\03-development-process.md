# Development Process & Planning

## Systematic Approach (Mandatory First Steps)
0.  **Load Project Context:** At the very beginning of any task, read all core files from the `memory-bank/` directory (as per global `memory-bank.md` rule) to ensure full awareness of the project's current state, learnings, and established patterns.
1.  **Analyze Request & Context:** Thoroughly understand the user's request and analyze any provided code context (@ references).
2.  **Gather Information & Clarify:** Identify required information. **List all assumptions and uncertainties.** Ask clarifying questions *before* proceeding if ambiguity exists. Do not make major assumptions without confirmation.
3.  **Propose Implementation Plan:** Create a concise implementation plan in Markdown format. Outline:
    *   The proposed approach/algorithm.
    *   Files to be created or modified.
    *   Key classes/functions to be implemented/changed.
    *   Potential challenges or trade-offs.
    *   How testing will be addressed (unit, integration).
    *   If proposing initial configuration files (e.g., `config.ini`) with placeholders for external data (e.g., Kaggle datasets), and if project documents provide specific names or types for these data sources, proactively offer to refine these placeholders using available tools (e.g., MCP search, document analysis) to be more specific, even if the data is not yet present. Clearly label them as refined placeholders.
4.  Await Approval: Wait for explicit user approval of the plan before writing *any* implementation code.
    *   **Note on Document Generation:** For tasks primarily involving complex document generation or synthesis (e.g., creating detailed reports, plans, or analyses from multiple sources like this one), if the user's initial request clearly defines the desired output structure and sources, the AI may proceed with generation after initial information gathering and context loading. If ambiguity exists in the desired structure or approach for the document, a plan for its generation should be proposed and approved.

## Implementation Phase (After Plan Approval)
- Implement the solution according to the approved plan.
- Adhere strictly to all rules in the `.clinerules/` folder (Architecture, Quality, Testing, Security, Documentation).
- **Configuration File Consistency:** When modifying configuration files (e.g., `cline_mcp_settings.json`, `package.json`, build scripts):
    - Before adding new entries, always inspect existing entries for patterns, such as full paths for commands, required environment variables, or specific argument structures.
    - Prioritize consistency with existing, working configurations to minimize errors.
- **Shell Command Execution:** When using `execute_command`:
    - **Consult Memory Bank:** Before attempting complex commands or when troubleshooting, check `memory-bank/activeContext.md` (Learnings & Project Insights) and `memory-bank/techContext.md` for known shell behaviors or previously encountered issues.
    - Be mindful of the user's default shell (provided in SYSTEM INFORMATION). However, always inspect the actual command output for error messages that might indicate a different shell is in use (e.g., PowerShell errors when `cmd.exe` was expected).
    - If complex command chaining (e.g., using `&&` or `;`) fails:
        1.  **Prioritize attempting commands individually** as the first fallback. This is often the most reliable solution.
            *   If a command *must* be run in a specific directory and `cd ... && command` (or shell equivalent like `;`) is problematic due to `execute_command`'s isolated instances or shell interpretation:
                *   For PowerShell (if inferred or confirmed): Consider `powershell -Command "Push-Location C:\\path\\to\\dir; your_command_here; Pop-Location"` to temporarily change directory for the command's execution.
                *   For other shells, investigate equivalent mechanisms or tool-specific options (e.g., `npm --prefix /path/to/dir <command>` for npm, though verify its behavior for the specific npm command as its effectiveness can vary, as seen with `npm install`).
        2.  If individual commands are not feasible and chaining is necessary, then attempt shell-specific chaining operators based on error messages or inferred shell type (e.g., use `;` for PowerShell if `&&` fails and PowerShell is suspected).
    - Always prioritize clarity and reliability. If unsure about complex chaining, use individual commands.
    - If asked to perform shell operations that modify the persistent state of the user's active terminal (e.g., activating a virtual environment, setting environment variables for the session), explain that `execute_command` runs in isolated instances and cannot perform these actions directly in the user's terminal. Guide the user to run such commands themselves.
- **Iterative `replace_in_file` Operations:** When using `replace_in_file` for files requiring numerous or extensive changes (e.g., widespread formatting adjustments combined with many content corrections like typo fixing across a large document), prefer breaking down the modifications into several smaller, sequential `replace_in_file` calls. Process the file in logical chunks or sections. This reduces the risk of SEARCH block mismatches due to complexity or slight deviations in the expected content and minimizes tool failure, ensuring more reliable updates.

### 3.1. Action Verification (Mandatory after file/system modifying tool use)
- **Verify File/Directory Operations:** After using `write_to_file`, `replace_in_file`, or `execute_command` for file/directory creation or modification, **explicitly check the subsequent `environment_details`** (specifically the 'Current Working Directory Files' list or relevant tool output) in the next thinking step.
- **Confirm Success:** Ensure the intended file/directory exists or was modified as expected.
- **Address Failures:** If verification fails (e.g., file not listed, command output shows errors), re-attempt the action or formulate a corrected approach in the next step. Do not proceed with dependent tasks until the prerequisite action is verified.
- **Verify Command Execution:** After `execute_command`, review the command's output in the user's response. If errors are reported or the output is unexpected, address these before assuming success.

## Debugging (If Applicable)
- If asked to debug, follow a methodical process: attempt to reproduce, analyze logs/errors provided, form hypotheses, suggest specific checks or tests to isolate the cause.
