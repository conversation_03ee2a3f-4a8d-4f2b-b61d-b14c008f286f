[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "context-portal-mcp"
version = "0.1.8"
authors = [
  { name="Your Name", email="<EMAIL>" }, # Placeholder
]
description = "A Model Context Protocol (MCP) server for managing structured project context."
readme = "README.md"
license = {file = "LICENSE"}
requires-python = ">=3.8"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
]
dependencies = [
    "fastapi",
    "uvicorn[standard]",
    "pydantic",
    "mcp[cli]",
    "sentence-transformers",
    "chromadb",
]

[project.urls]
"Homepage" = "https://github.com/GreatScottyMac/context-portal" # Assuming this is the main repo

[project.scripts]
conport-mcp = "context_portal_mcp.main:cli_entry_point" # Define an entry point script

[tool.setuptools.packages.find]
where = ["src"] # Look for packages in the src directory