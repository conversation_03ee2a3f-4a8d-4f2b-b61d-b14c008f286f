[General]
log_file = logs/etl.log
log_level = DEBUG  ; DEBUG, INFO, WARNING, ERROR, CRITICAL
data_directory = data/
schema_directory = database/schema_ddl/ ; Path to DDL scripts

[API_NBA]
delay_seconds = 2  ; Politeness delay between API calls
timeout_seconds = 30 ; Request timeout
retries = 3 ; Number of retries on failure

[BasketballReference]
delay_seconds = 5
; Politeness delay for any direct scraping if done outside the library
timeout_seconds = 30
retries = 3

[Spotrac]
delay_seconds = 5
timeout_seconds = 30
retries = 3

[KaggleEoinMoore]
# Define paths to specific Kaggle dataset files or directories.
# These are placeholders based on known dataset names, as actual data is not yet downloaded.
# Adjust to actual file names and locations within the ${General:data_directory}/kaggle_datasets/ later.

# From Eoin <PERSON>'s "Complete NBA Database and Historical Box Scores" (expected CSVs)
eoin_moore_player_statistics_csv = ${General:data_directory}/kaggle_datasets/eoin/PlayerStatistics.csv
eoin_moore_team_statistics_csv = ${General:data_directory}/kaggle_datasets/eoin/TeamStatistics.csv
eoin_moore_game_logs_csv = ${General:data_directory}/kaggle_datasets/eoin/Games.csv
eoin_moore_league_schedule_csv = ${General:data_directory}/kaggle_datasets/eoin/LeagueSchedule24_25.csv
eoin_moore_players_csv = ${General:data_directory}/kaggle_datasets/eoin/Players.csv

[KaggleEoinMooreTables]
player_statistics_table = player_statistics
team_statistics_table = team_statistics
game_logs_table = game_logs
league_schedule_table = eoin_league_schedule
players_table = eoin_players

[KaggleWyattOWalsh]
# CSV file paths for Wyatt O'Walsh's dataset
wyatt_team_history_csv = ${General:data_directory}/kaggle_datasets/wyatt/csv/team_history.csv
wyatt_team_csv = ${General:data_directory}/kaggle_datasets/wyatt/csv/team.csv
# Add other Wyatt O'Walsh CSVs here as needed

# Corresponding table names for Wyatt O'Walsh's CSVs (can also be defined here or derived in code)
wyatt_team_history_table = wyatt_team_history # Example, adjust as needed
wyatt_team_table = wyatt_teams # Example, adjust as needed

[Database]
# Credentials (host, port, dbname, user, password) will be in .env
# schema_directory is in [General]
connection_timeout = 30 ; Optional: connection timeout in seconds

[ColumnRenames_PlayerStatistics]
firstname = firstName
lastname = lastName
personid = personId
gameid = gameId
gamedate = gameDate
playerteamcity = playerteamCity
playerteamname = playerteamName
opponentteamcity = opponentteamCity
opponentteamname = opponentteamName
gametype = gameType
gamelabel = gameLabel
gamesublabel = gameSubLabel
seriesgamenumber = seriesGameNumber
win = win
home = home
numminutes = numMinutes
points = points
assists = assists
blocks = blocks
steals = steals
fieldgoalsattempted = fieldGoalsAttempted
fieldgoalsmade = fieldGoalsMade
fieldgoalspercentage = fieldGoalsPercentage
threepointersattempted = threePointersAttempted
threepointersmade = threePointersMade
threepointerspercentage = threePointersPercentage
freethrowsattempted = freeThrowsAttempted
freethrowsmade = freeThrowsMade
freethrowspercentage = freeThrowsPercentage
reboundsdefensive = reboundsDefensive
reboundsoffensive = reboundsOffensive
reboundstotal = reboundsTotal
foulspersonal = foulsPersonal
turnovers = turnovers
plusminuspoints = plusMinusPoints

[ColumnRenames_Players]
personid = person_id
firstname = first_name
lastname = last_name
birthdate = birthdate
lastattended = last_attended
country = country
height = height_inches
bodyweight = body_weight_lbs
guard = is_guard
forward = is_forward
center = is_center
draftyear = draft_year
draftround = draft_round
draftnumber = draft_number

[ColumnRenames_TeamStatistics]
gameid = gameId
gamedate = gameDate
teamcity = teamCity
teamname = teamName
teamid = teamId
opponentteamcity = opponentTeamCity
opponentteamname = opponentTeamName
opponentteamid = opponentTeamId
home = home
win = win
teamscore = teamScore
opponentscore = opponentScore
assists = assists
blocks = blocks
steals = steals
fieldgoalsattempted = fieldGoalsAttempted
fieldgoalsmade = fieldGoalsMade
fieldgoalspercentage = fieldGoalsPercentage
threepointersattempted = threePointersAttempted
threepointersmade = threePointersMade
threepointerspercentage = threePointersPercentage
freethrowsattempted = freeThrowsAttempted
freethrowsmade = freeThrowsMade
freethrowspercentage = freeThrowsPercentage
reboundsdefensive = reboundsDefensive
reboundsoffensive = reboundsOffensive
reboundstotal = reboundsTotal
foulspersonal = foulsPersonal
turnovers = turnovers
plusminuspoints = plusMinusPoints
numminutes = numMinutes
q1points = q1Points
q2points = q2Points
q3points = q3Points
q4points = q4Points
benchpoints = benchPoints
biggestlead = biggestLead
biggestscoringrun = biggestScoringRun
leadchanges = leadChanges
pointsfastbreak = pointsFastBreak
pointsfromturnovers = pointsFromTurnovers
pointsinthepaint = pointsInThePaint
pointssecondchance = pointsSecondChance
timestied = timesTied
timeoutsremaining = timeoutsRemaining
seasonwins = seasonWins
seasonlosses = seasonLosses
coachid = coachId

[ColumnRenames_Games]
gameid = gameId
gamedate = gameDate
hometeamcity = hometeamCity
hometeamname = hometeamName
hometeamid = hometeamId
awayteamcity = awayteamCity
awayteamname = awayteamName
awayteamid = awayteamId
homescore = homeScore
awayscore = awayScore
winner = winner
gametype = gameType
attendance = attendance
arenaid = arenaId
gamelabel = gameLabel
gamesublabel = gameSubLabel
seriesgamenumber = seriesGameNumber

[ColumnRenames_LeagueSchedule]
gameid = game_id
gamedatetimeest = game_datetime_est
gameday = game_day
arenacity = arena_city
arenastate = arena_state
arenaname = arena_name
gamelabel = game_label
gamesublabel = game_sub_label
gamesubtype = game_subtype
gamesequence = game_sequence
seriesgamenumber = series_game_number
seriestext = series_text
weeknumber = week_number
hometeamid = hometeam_id
awayteamid = awayteam_id
