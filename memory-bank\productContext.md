# Product Context

## Problem Statement
*   Accessing comprehensive, granular, and historically deep NBA (and related leagues like BAA, NBL, ABA) data for personal analysis and projects is often fragmented, requiring users to consult multiple sources, deal with varying data formats, and lack a unified, queryable repository.
*   Many existing datasets might be incomplete, not cover the full historical range (1946-present), or lack the specific granularity needed for in-depth research (e.g., detailed contract information, specific award voting, full transaction histories).

## Solution Overview
*   This project aims to solve the problem by creating a local, queryable PostgreSQL database populated with a highly granular set of historical NBA, BAA, NBL, and ABA data, spanning from 1946 to 2025.
*   It will achieve this by developing an ETL pipeline (core components like `etl_orchestrator.py`, `extractors.py`, `transformers.py`, `loaders.py` are in place) that ingests data from various free and open-source providers (Kaggle - Eoin Moore & Wyatt O'Walsh, NBA.com API, Basketball-Reference.com), cleans and transforms this data, resolves conflicts, and loads it into a well-defined database schema.
*   The result will be a single, comprehensive, and locally accessible dataset.

## User Experience Goals
*   **Accessibility:** Provide a straightforward way for a user (with SQL knowledge) to access a vast amount of historical basketball data locally without needing to scrape or manage multiple data sources themselves.
*   **Queryability:** Enable complex data analysis and exploration through standard SQL queries against a well-structured and indexed relational database.
*   **Comprehensiveness:** Offer a dataset that is as complete and granular as possible within the constraints of available free sources, covering various aspects of the game (player stats, game details, team info, transactions, awards, contracts, etc.).
*   **Reliability:** Ensure data is cleaned, standardized, and conflicts are resolved based on a defined precedence, leading to a more trustworthy dataset.
*   **Understandability:** Provide clear documentation (Data Dictionary, README) to help users understand the schema, data sources, and ETL process.
