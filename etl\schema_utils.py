#!/usr/bin/env python3
# Expected location: c:\Users\<USER>\Projects\nbadb\Database\etl\schema_utils.py

import logging
import pandas as pd
import os
import sys
import json
from pathlib import Path

# Configure logging
logger = logging.getLogger('etl_schema_utils')

class SchemaManager:
    """
    Utility class for managing and validating database schemas during ETL processes.
    """
    
    def __init__(self, db_manager):
        """
        Initialize the schema manager.
        
        Args:
            db_manager: DatabaseManager instance for database connections.
        """
        self.db_manager = db_manager
        self.schema_cache = {}
    
    def get_table_schema(self, table_name):
        """
        Get the schema for a specific table.
        
        Args:
            table_name (str): Name of the table.
            
        Returns:
            dict: Dictionary of column names and their PostgreSQL data types.
        """
        # Check cache first
        if table_name in self.schema_cache:
            return self.schema_cache[table_name]
        
        # Query the database for table schema
        query = """
        SELECT column_name, data_type, character_maximum_length, is_nullable
        FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = %s
        ORDER BY ordinal_position
        """
        
        try:
            columns = self.db_manager.execute_query(query, (table_name,), fetch_mode='all')
            
            if not columns:
                logger.warning(f"No schema information found for table '{table_name}'")
                return {}
            
            schema = {}
            for col in columns:
                col_name, data_type, max_length, is_nullable = col
                
                # For character types with length, include the length
                if max_length and data_type in ('character varying', 'character'):
                    data_type = f"{data_type}({max_length})"
                
                schema[col_name] = {
                    'data_type': data_type,
                    'nullable': is_nullable == 'YES'
                }
            
            # Cache the schema
            self.schema_cache[table_name] = schema
            
            return schema
            
        except Exception as e:
            logger.error(f"Error getting schema for table '{table_name}': {str(e)}")
            return {}
    
    def get_all_tables(self):
        """
        Get a list of all tables in the database.
        
        Returns:
            list: List of table names.
        """
        query = """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        ORDER BY table_name
        """
        
        try:
            result = self.db_manager.execute_query(query, fetch_mode='all')
            return [row[0] for row in result]
        except Exception as e:
            logger.error(f"Error getting table list: {str(e)}")
            return []
    
    def get_table_constraints(self, table_name):
        """
        Get constraints for a specific table.
        
        Args:
            table_name (str): Name of the table.
            
        Returns:
            dict: Dictionary of constraint information.
        """
        # Query for primary key
        pk_query = """
        SELECT a.attname
        FROM pg_index i
        JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
        WHERE i.indrelid = %s::regclass AND i.indisprimary
        """
        
        # Query for unique constraints
        unique_query = """
        SELECT con.conname AS constraint_name,
               array_agg(att.attname ORDER BY array_position(con.conkey, att.attnum)) AS columns
        FROM pg_constraint con
        JOIN pg_attribute att ON att.attrelid = con.conrelid AND att.attnum = ANY(con.conkey)
        WHERE con.conrelid = %s::regclass AND con.contype = 'u'
        GROUP BY con.conname
        """
        
        # Query for foreign keys
        fk_query = """
        SELECT
            tc.constraint_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM
            information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = %s
        """
        
        constraints = {
            'primary_key': [],
            'unique_constraints': [],
            'foreign_keys': []
        }
        
        try:
            # Get primary key
            pk_result = self.db_manager.execute_query(pk_query, (table_name,), fetch_mode='all')
            constraints['primary_key'] = [row[0] for row in pk_result]
            
            # Get unique constraints
            unique_result = self.db_manager.execute_query(unique_query, (table_name,), fetch_mode='all')
            for row in unique_result:
                constraints['unique_constraints'].append({
                    'name': row[0],
                    'columns': row[1]
                })
            
            # Get foreign keys
            fk_result = self.db_manager.execute_query(fk_query, (table_name,), fetch_mode='all')
            for row in fk_result:
                constraints['foreign_keys'].append({
                    'name': row[0],
                    'column': row[1],
                    'foreign_table': row[2],
                    'foreign_column': row[3]
                })
            
            return constraints
            
        except Exception as e:
            logger.error(f"Error getting constraints for table '{table_name}': {str(e)}")
            return constraints
    
    def validate_dataframe_for_table(self, df, table_name):
        """
        Validate a DataFrame against a table schema.
        
        Args:
            df (pandas.DataFrame): DataFrame to validate.
            table_name (str): Name of the target table.
            
        Returns:
            tuple: (is_valid, issues) where is_valid is a boolean and issues is a list of validation issues.
        """
        if df.empty:
            return True, []
        
        schema = self.get_table_schema(table_name)
        if not schema:
            return False, [f"No schema found for table '{table_name}'"]
        
        issues = []
        
        # Check for required columns
        for col_name, col_info in schema.items():
            if not col_info['nullable'] and col_name not in df.columns:
                issues.append(f"Required column '{col_name}' missing from DataFrame")
        
        # Check data types
        for col_name, col_info in schema.items():
            if col_name in df.columns:
                pg_type = col_info['data_type']
                
                # Basic type checking - this could be expanded for more sophisticated validation
                if pg_type.startswith('int') and df[col_name].dtype not in ('int64', 'int32', 'int16', 'int8'):
                    issues.append(f"Column '{col_name}' should be integer type, got {df[col_name].dtype}")
                
                elif pg_type.startswith('float') or pg_type.startswith('double') or pg_type.startswith('numeric'):
                    if df[col_name].dtype not in ('float64', 'float32'):
                        issues.append(f"Column '{col_name}' should be float type, got {df[col_name].dtype}")
                
                elif pg_type.startswith('bool') and df[col_name].dtype != 'bool':
                    issues.append(f"Column '{col_name}' should be boolean type, got {df[col_name].dtype}")
                
                elif pg_type.startswith('timestamp') or pg_type.startswith('date'):
                    if not pd.api.types.is_datetime64_any_dtype(df[col_name]):
                        issues.append(f"Column '{col_name}' should be datetime type, got {df[col_name].dtype}")
        
        # Check for non-nullable columns with NULL values
        for col_name, col_info in schema.items():
            if col_name in df.columns and not col_info['nullable']:
                if df[col_name].isna().any():
                    issues.append(f"Column '{col_name}' is not nullable but contains NULL values")
        
        return len(issues) == 0, issues
    
    def get_table_mapping_for_source(self, source_name):
        """
        Get table mappings for a specific data source.
        
        Args:
            source_name (str): Name of the data source.
            
        Returns:
            dict: Dictionary of source table to target table mappings.
        """
        # This is a placeholder - in a real implementation, you might load this from a config file
        # or database table
        
        mappings = {
            'kaggle_eoin_moore': {
                'player_statistics': {
                    'target_table': 'players',
                    'column_mappings': {
                        'player_name': 'player_name',
                        'team': 'team_name',
                        'position': 'position'
                        # Add more mappings as needed
                    }
                },
                'team_statistics': {
                    'target_table': 'teams',
                    'column_mappings': {
                        'team_name': 'team_name',
                        'wins': 'wins',
                        'losses': 'losses'
                        # Add more mappings as needed
                    }
                }
            },
            'kaggle_wyatt_owalsh': {
                # Add mappings for Wyatt O'Walsh dataset
            },
            'nba_api': {
                # Add mappings for NBA API
            },
            'basketball_reference': {
                # Add mappings for Basketball Reference
            }
        }
        
        return mappings.get(source_name, {})
    
    def export_schema_to_json(self, output_file='schema.json'):
        """
        Export the database schema to a JSON file.
        
        Args:
            output_file (str): Path to the output JSON file.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        tables = self.get_all_tables()
        schema = {}
        
        for table in tables:
            schema[table] = {
                'columns': self.get_table_schema(table),
                'constraints': self.get_table_constraints(table)
            }
        
        try:
            with open(output_file, 'w') as f:
                json.dump(schema, f, indent=2)
            
            logger.info(f"Schema exported to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting schema to {output_file}: {str(e)}")
            return False
    
    def import_schema_from_json(self, input_file='schema.json'):
        """
        Import the database schema from a JSON file.
        
        Args:
            input_file (str): Path to the input JSON file.
            
        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            with open(input_file, 'r') as f:
                schema = json.load(f)
            
            # Update cache
            self.schema_cache = {table: info['columns'] for table, info in schema.items()}
            
            logger.info(f"Schema imported from {input_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error importing schema from {input_file}: {str(e)}")
            return False


# Example usage
if __name__ == "__main__":
    # Add parent directory to path for imports
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    from database.db_manager import DatabaseManager
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    # Create a DatabaseManager instance
    db_manager = DatabaseManager()
    
    # Create a SchemaManager instance
    schema_manager = SchemaManager(db_manager)
    
    # Export the schema to a JSON file
    schema_manager.export_schema_to_json('schema.json')
    
    # Print all tables
    tables = schema_manager.get_all_tables()
    print(f"Tables: {tables}")
    
    # Print schema for a specific table
    if tables:
        table_name = tables[0]
        schema = schema_manager.get_table_schema(table_name)
        print(f"Schema for table '{table_name}':")
        for col_name, col_info in schema.items():
            print(f"  {col_name}: {col_info}")
        
        constraints = schema_manager.get_table_constraints(table_name)
        print(f"Constraints for table '{table_name}':")
        print(f"  Primary Key: {constraints['primary_key']}")
        print(f"  Unique Constraints: {constraints['unique_constraints']}")
        print(f"  Foreign Keys: {constraints['foreign_keys']}")
