# Active Context

## Current Work Focus
*   **ETL Pipeline Debugging:** Addressing `NumericValueOutOfRange` error for `Players.csv` (target table `eoin_players`) by ensuring correct transformation logic is applied.

## Recent Changes
*   **`etl/etl_orchestrator.py` Update (Players - Corrected transform condition):** Changed `elif file_name == 'Players.csv':` to `elif file_name == 'Players':` in `_transform_eoin_moore_data` to ensure player-specific transformations (data cleaning, type conversion) are applied. This is intended to fix the `NumericValueOutOfRange` error.
*   **`config.ini` Update (Players - Corrected Keys):** Adjusted keys in `[ColumnRenames_Players]` (e.g., `height = height_inches`) to match standardized CSV column names, resolving `column "height" of relation "eoin_players" does not exist` error.
*   **`config.ini` Update (Players - Renamed Section):** Previously renamed section `[ColumnRenames_Players.csv]` to `[ColumnRenames_Players]`.
*   **`config.ini` Update (LeagueSchedule):** Previously added `[ColumnRenames_LeagueSchedule]` section.
*   **`config.ini` Update (Games - Corrected Casing):** Previously adjusted casing in the `[ColumnRenames_Games]` section.
*   **`config.ini` Update (Games - Initial):** Previously added `[ColumnRenames_Games]` section.
*   **`config.ini` Update (TeamStatistics - Corrected):** Previously replaced the `[ColumnRenames_TeamStatistics]` section.
*   **ETL Pipeline Implementation (Ongoing):**
    *   `etl/etl_orchestrator.py`: Main orchestrator for the ETL process, handles configuration, schema initialization, and coordinates data source processing.
    *   `etl/extractors.py`: Contains classes for extracting data from various sources (Kaggle CSVs, NBA API, Basketball-Reference).
    *   `etl/transformers.py`: Contains classes for transforming data (e.g., `WyattFranchiseTeamTransformer`). Specific transformation logic for Eoin Moore's data is also present in `etl_orchestrator.py`.
    *   `etl/loaders.py`: Contains classes for loading data into PostgreSQL, utilizing `DatabaseManager`.
    *   `etl/run_etl.py`: Command-line entry point for running the ETL pipeline.
    *   `etl/schema_utils.py`: Utilities for schema management and validation.
    *   `logs/`: Directory for ETL logs.
*   **Database Management (Stable):**
    *   `database/db_manager.py`: Robust class for managing PostgreSQL connections, executing DDL scripts, running queries, and loading pandas DataFrames into tables. Includes handling for `.env` file for credentials.
    *   `database/schema_ddl/`: Contains DDL scripts for creating tables.
*   **Data Storage Structure (Stable):**
    *   `data/`: Base directory for datasets.

## Next Steps
1.  **Test ETL Pipeline (Players Fix):** Execute `python etl/run_etl.py --sources kaggle_eoin_moore --tables eoin_players` to verify the `Players.csv` `NumericValueOutOfRange` fix.
2.  **Monitor ETL Logs:** Check `logs/etl.log` (and timestamped logs) for successful processing of `Players.csv` into `eoin_players`.
3.  **Address Further ETL Issues:** If the `Players.csv` error persists or new errors arise, further investigate the transformation logic or data.
4.  **Validate Data:** Once `Players.csv` and all other Eoin Moore datasets load successfully, perform basic data validation queries.

## Active Decisions & Considerations
*   The immediate decision was to correct the conditional logic in `_transform_eoin_moore_data` to ensure player-specific transformations are applied.
*   Monitoring log output closely after the next ETL run for `Players.csv` is critical.

## Important Patterns & Preferences
*   **Configuration-driven ETL:** Using `config.ini` for dynamic settings like column renames is a key pattern.
*   **Transform Identifier Matching:** Ensuring that identifiers used to trigger specific transformations (like `file_name` in `_transform_eoin_moore_data`) correctly match the values passed during processing.
*   **Iterative Debugging:** Addressing one table/error at a time, verifying, and then moving to the next.
*   Systematic execution of ETL steps: Extract, Transform, Load.
*   Modular code structure within the `etl/` and `database/` directories.
*   Comprehensive logging for ETL processes.

## Learnings & Project Insights
*   The ETL pipeline's standardization of CSV column names to lowercase, followed by `config.ini`-driven remapping to the database's expected (often camelCase) schema, is a critical and recurring pattern for this project.
*   `PlayerStatistics` processing (1.6M+ rows) was successful after applying this pattern.
*   `TeamStatistics` exhibited the exact same problem and was resolved with `config.ini` changes.
*   Other datasets from Eoin Moore (Games.csv, LeagueSchedule24_25.csv) also required similar `config.ini` treatment and were resolved.
*   The `NumericValueOutOfRange` error for `Players.csv` highlighted the importance of ensuring transformation logic is correctly triggered, not just defined.
*   `DatabaseManager` is a central piece for all database interactions.
*   Chunking is used for processing large CSV files.
