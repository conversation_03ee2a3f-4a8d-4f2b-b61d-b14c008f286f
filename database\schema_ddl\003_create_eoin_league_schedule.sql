CREATE TABLE IF NOT EXISTS public.eoin_league_schedule (
    game_id BIGINT PRIMARY KEY,
    game_datetime_est TIMESTAMPTZ,
    game_day VARCHAR(10),
    arena_city TEXT,
    arena_state VARCHAR(10),
    arena_name TEXT,
    game_label TEXT,
    game_sub_label TEXT,
    game_subtype TEXT,
    game_sequence INTEGER,
    series_game_number TEXT,
    series_text TEXT,
    week_number INTEGER,
    hometeam_id BIGINT,
    awayteam_id BIGINT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Optional: Add a trigger to update 'updated_at' timestamp on row modification
CREATE OR REPLACE FUNCTION fn_update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'trg_eoin_league_schedule_update_timestamp'
        AND tgrelid = 'public.eoin_league_schedule'::regclass
    ) THEN
        CREATE TRIGGER trg_eoin_league_schedule_update_timestamp
        BEFORE UPDATE ON public.eoin_league_schedule
        FOR EACH ROW
        EXECUTE FUNCTION fn_update_timestamp();
    END IF;
END
$$;

COMMENT ON TABLE public.eoin_league_schedule IS 'Stores league schedule information from Eoin Moore''s dataset, typically for upcoming or preseason games.';
COMMENT ON COLUMN public.eoin_league_schedule.game_id IS 'Unique identifier for the game (from gameId).';
COMMENT ON COLUMN public.eoin_league_schedule.game_datetime_est IS 'Estimated game date and time (from gameDateTimeEst).';
