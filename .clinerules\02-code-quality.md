# Code Quality & Maintainability

## Clean Code
- **Meaningful Names:** Use clear, descriptive, self-documenting names for variables, functions, classes, etc.
- **Small, Focused Functions:** Functions should do one thing well and be easily understandable (ideally fitting on a screen).
- **Clear Control Flow:** Minimize nesting complexity. Use guard clauses and extract methods to improve readability.
- **Comments:** Comment *why* something is done, not *what* the code does (the code should explain the 'what'). Avoid redundant comments.
- **Error Handling:** Implement consistent and informative error handling. Use exceptions appropriately; avoid swallowing exceptions or returning error codes where exceptions are better. Refer to project's error handling pattern (e.g., `/src/utils/errors.ts`).
- **Formatting:** Adhere strictly to the project's established code formatting standards (e.g., <PERSON>tti<PERSON>, ESLint rules).

## Code Organization
- **Logical Cohesion:** Group related functionality within modules/classes.
- **Encapsulation:** Hide implementation details. Minimize visibility (e.g., use `private` where possible). Expose well-defined interfaces.
- **Dependency Management:** Aim for loose coupling between modules. Use Dependency Injection.
- **Package Structure:** Follow the project's defined package/directory structure.
- **Composition over Inheritance:** Prefer composition unless inheritance provides a clear and appropriate model. Avoid deep inheritance hierarchies.
- **Consistent Patterns:** Apply established design patterns consistently.

## Technical Debt
- **Boy Scout Rule:** Leave code cleaner than you found it. Make small improvements when working in an area.
- **Refactoring:** Proactively suggest refactoring opportunities to improve structure or clarity without changing functionality. Identify and flag potential technical debt.
- **Completeness:** DO NOT BE LAZY. DO NOT OMIT CODE. Provide full function/class/component definitions unless specifically asked for a snippet or placeholder. Ensure imports are included.