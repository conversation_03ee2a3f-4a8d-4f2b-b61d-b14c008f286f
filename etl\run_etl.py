#!/usr/bin/env python3
# Expected location: c:\Users\<USER>\Projects\nbadb\Database\etl\run_etl.py

import os
import sys
import argparse
import logging
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the ETL orchestrator
from etl.etl_orchestrator import ETLOrchestrator

def setup_logging(log_file=None, log_level='INFO'):
    """
    Set up logging configuration.
    
    Args:
        log_file (str, optional): Path to log file. If None, log to console only.
        log_level (str): Logging level. Default 'INFO'.
    """
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Set up default log file if not provided
    if log_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = f'logs/etl_{timestamp}.log'
    
    # Configure logging
    level = getattr(logging, log_level.upper())
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger('run_etl')
    logger.info(f"Logging initialized at level {log_level}")
    logger.info(f"Log file: {log_file}")
    
    return logger

def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description='NBA Database ETL Pipeline')
    
    parser.add_argument('--config', default='config.ini',
                        help='Path to configuration file (default: config.ini)')
    
    parser.add_argument('--log-file',
                        help='Path to log file (default: logs/etl_TIMESTAMP.log)')
    
    parser.add_argument('--log-level', default='INFO',
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='Logging level (default: INFO)')
    
    parser.add_argument('--sources', nargs='+',
                        choices=['kaggle_eoin_moore', 'kaggle_wyatt_owalsh', 'nba_api', 'basketball_reference', 'all'],
                        default=['all'],
                        help='Data sources to process (default: all)')
    
    parser.add_argument('--tables', nargs='+',
                        help='Specific tables to process (default: all tables)')
    
    parser.add_argument('--dry-run', action='store_true',
                        help='Perform a dry run without actually loading data')
    
    return parser.parse_args()

def main():
    """Main entry point for the ETL pipeline."""
    # Parse command line arguments
    args = parse_args()
    
    # Set up logging
    logger = setup_logging(args.log_file, args.log_level)
    
    try:
        logger.info("Starting NBA Database ETL Pipeline")
        
        # Process 'all' sources
        if 'all' in args.sources:
            sources = ['kaggle_eoin_moore', 'kaggle_wyatt_owalsh', 'nba_api', 'basketball_reference']
        else:
            sources = args.sources
        
        # Initialize ETL orchestrator
        etl = ETLOrchestrator(config_path=args.config)
        
        if args.dry_run:
            logger.info("Performing dry run - no data will be loaded")
            # Just log what would be done
            for source in sources:
                logger.info(f"Would process source: {source}")
                if args.tables:
                    logger.info(f"Would process tables: {', '.join(args.tables)}")
                else:
                    logger.info("Would process all tables")
            
            logger.info("Dry run completed successfully")
            return 0
        
        # Run the ETL pipeline
        success = etl.run_etl_pipeline(sources=sources, tables=args.tables)
        
        if success:
            logger.info("ETL pipeline completed successfully")
            return 0
        else:
            logger.error("ETL pipeline completed with errors")
            return 1
    
    except Exception as e:
        logger.exception(f"Unhandled exception in ETL pipeline: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
