INSERT INTO franchises (
    franchise_id,
    franchise_name_common,
    first_season_id,
    last_season_id,
    notes,
    created_at,
    updated_at
  )
VALUES (
    franchise_id:integer,
    'franchise_name_common:text',
    first_season_id:integer,
    last_season_id:integer,
    'notes:text',
    'created_at:timestamp with time zone',
    'updated_at:timestamp with time zone'
  );
INSERT INTO leagues (
        league_id,
        league_name,
        league_abbreviation,
        founded_year,
        disbanded_year,
        notes,
        created_at,
        updated_at
      )
    VALUES (
        league_id:integeSchemasr,
        'league_name:character varying',
        'league_abbreviation:character varying',
        founded_year:integer,
        disbanded_year:integer,
        'notes:text',
        'created_at:timestamp with time zone',
        'updated_at:timestamp with time zone'
      );
INSERT INTO seasons (
    season_id,
    season_name,
    season_abbreviation,
    season_start_date,
    season_end_date,
    notes,
    created_at,
    updated_at
  )
VALUES (
    season_id:integer,
    'season_name:text',
    'season_abbreviation:text',
    'season_start_date:timestamp with time zone',
    'season_end_date:timestamp with time zone',
    'notes:text',
    'created_at:timestamp with time zone',
    'updated_at:timestamp with time zone'
  );    
INSERT INTO teams (
    team_id,
    team_name_common,
    franchise_id,
    season_id,
    notes,
    created_at,
    updated_at
  )
VALUES (
    team_id:integer,
    'team_name_common:text',
    franchise_id:integer,
    season_id:integer,
    'notes:text',
    'created_at:timestamp with time zone',
    'updated_at:timestamp with time zone'
  );                INSERT INTO franchises (
      franchise_id,
      franchise_name_common,
      first_season_id,
      last_season_id,
      notes,
      created_at,
      updated_at
    )
  VALUES (
      franchise_id:integer,
      'franchise_name_common:text',
      first_season_id:integer,
      last_season_id:integer,
      'notes:text',
      'created_at:timestamp with time zone',
      'updated_at:timestamp with time zone'
    );INSERT INTO franchises (
        franchise_id,
        franchise_name_common,
        first_season_id,
        last_season_id,
        notes,
        created_at,
        updated_at
      )
    VALUES (
        franchise_id:integer,
        'franchise_name_common:text',
        first_season_id:integer,
        last_season_id:integer,
        'notes:text',
        'created_at:timestamp with time zone',
        'updated_at:timesINSERT INTO franchises (
            franchise_id,
            franchise_name_common,
            first_season_id,
            last_season_id,
            notes,
            created_at,
            updated_at
          )
        VALUES (
            franchise_id:integer,
            'franchise_name_common:text',
            first_season_id:integer,
            last_season_id:integer,
            'notes:text',
            'created_at:timestamp with time zone',
            'updated_at:timestamp with time zone'
          );tamp with time zone'
      );INSERT INTO franchises (
          franchise_id,
          franchise_name_common,
          first_season_id,
          last_season_id,
          notes,
          created_at,
          updated_at
        )
      VALUES (
          franchise_id:integerINSERT INTO franchises (
              franchise_id,
              franchise_name_common,
              first_season_id,
              last_season_id,
              notes,
              created_at,
              updated_at
            )
          VALUES (
              franchise_id:integerINSERT INTO franchises (
                  franchise_id,
                  franchise_name_common,
                  first_season_id,
                  last_season_id,
                  notes,
                  created_at,
                  updated_at
                )
              VALUES (
                  franchise_id:integerINSERT INTO franchises (
                      franchise_id,
                      franchise_name_common,
                      first_season_id,
                      last_season_id,
                      notes,
                      created_at,
                      updated_at
                    )
                  VALUES (
                      franchise_id:integer,
                      'franchise_name_common:text',
                      first_season_id:integer,
                      last_season_id:integer,
                      'notes:text',
                      'created_at:timestamp with time zone',
                      'updated_at:timestamp with time zone'
                    );,
                  'franchise_name_common:text',
                  first_season_id:integer,
                  last_season_id:integer,
                  'notes:text',
                  'created_at:timestamp with time zone',
                  'updated_at:timestamp with time zone'
                );,
              'franchise_name_common:text',
              first_season_id:integer,
              last_season_id:integer,
              'notes:text',
              'created_at:timestamp with time zone',
              'updated_at:timestamp with time zone'
            );,
          'franchise_name_common:text',
          first_season_id:integer,
          last_season_id:integer,
          'notes:text',
          'created_at:timestamp with time zone',
          'updated_at:timestamp with time zone'
        );SELECT team_id, season_year FROM public.team_info_common;