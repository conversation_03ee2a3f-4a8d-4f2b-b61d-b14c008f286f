#!/usr/bin/env python3
# Expected location: c:\Users\<USER>\Projects\nbadb\Database\etl\loaders.py

import logging
import pandas as pd
import os
import sys

# Configure logging
logger = logging.getLogger('etl_loaders')

class BaseLoader:
    """Base class for all data loaders."""
    
    def __init__(self, db_manager):
        """
        Initialize the loader.
        
        Args:
            db_manager (DatabaseManager): Database manager instance.
        """
        self.db_manager = db_manager
    
    def load(self, data, table_name, if_exists='append', **kwargs):
        """
        Load data into the database.
        
        Args:
            data (pandas.DataFrame): Data to load.
            table_name (str): Name of the target table.
            if_exists (str): {'append', 'replace', 'fail'}. Default 'append'.
            **kwargs: Additional arguments for the loader.
            
        Returns:
            int: Number of rows loaded.
        """
        raise NotImplementedError("Subclasses must implement load()")


class PostgreSQLLoader(BaseLoader):
    """Loader for PostgreSQL database."""
    
    def load(self, data, table_name, if_exists='append', chunksize=1000, unique_constraint_cols=None):
        """
        Load data into PostgreSQL database.
        
        Args:
            data (pandas.DataFrame): Data to load.
            table_name (str): Name of the target table.
            if_exists (str): {'append', 'replace', 'fail'}. Default 'append'.
            chunksize (int): Number of rows to insert at once. Default 1000.
            unique_constraint_cols (list): List of columns that form a unique constraint.
                Used for ON CONFLICT DO UPDATE logic when appending.
                
        Returns:
            int: Number of rows loaded.
        """
        if data.empty:
            logger.info(f"No data to load into table '{table_name}'")
            return 0
        
        logger.info(f"Loading {len(data)} rows into table '{table_name}'")
        
        try:
            # Use the DatabaseManager's load_df_to_table method
            rows_loaded = self.db_manager.load_df_to_table(
                df=data,
                table_name=table_name,
                if_exists=if_exists,
                chunksize=chunksize,
                unique_constraint_cols=unique_constraint_cols
            )
            
            logger.info(f"Successfully loaded {rows_loaded} rows into table '{table_name}'")
            return rows_loaded
            
        except Exception as e:
            logger.error(f"Error loading data into table '{table_name}': {str(e)}", exc_info=True)
            raise

    def load_franchises_and_teams(self, franchises_df, teams_df, franchise_table_name='public.franchises', team_table_name='public.teams'):
        """
        Loads franchise and team data, handling the dependency between them.
        1. Loads franchises and retrieves their generated IDs.
        2. Maps these IDs to the teams DataFrame.
        3. Loads the teams DataFrame.

        Args:
            franchises_df (pd.DataFrame): DataFrame for franchises. 
                                          Must contain 'source_franchise_id' and other franchise columns.
            teams_df (pd.DataFrame): DataFrame for teams. 
                                     Must contain 'source_franchise_id' and other team columns.
            franchise_table_name (str): Name of the franchise table.
            team_table_name (str): Name of the team table.
            
        Returns:
            dict: {'franchises_loaded': count, 'teams_loaded': count}
        """
        if franchises_df.empty:
            logger.info(f"No franchise data to load into {franchise_table_name}.")
            # Still try to load teams if teams_df is not empty and has 'franchise_id' already
            if not teams_df.empty and 'franchise_id' in teams_df.columns:
                 teams_loaded_count = self.load(teams_df, team_table_name, if_exists='append', unique_constraint_cols=['franchise_id', 'season_id'])
                 return {'franchises_loaded': 0, 'teams_loaded': teams_loaded_count}
            return {'franchises_loaded': 0, 'teams_loaded': 0}

        source_franchise_id_map = {} # To map source_id to new db_id
        franchises_loaded_count = 0
        
        cols_to_insert_franchise = ['franchise_name_common', 'first_season_id', 'last_season_id', 'notes']
        # Ensure 'source_franchise_id' is present for mapping, even if not inserted
        franchise_df_for_load = franchises_df[cols_to_insert_franchise + ['source_franchise_id']].copy()

        try:
            # Ensure we have a connection
            if self.db_manager.conn is None or self.db_manager.conn.closed:
                self.db_manager.connect()
            
            # Use the existing cursor from db_manager
            cursor = self.db_manager.cursor
            for _, row in franchise_df_for_load.iterrows():
                    first_season_id = row['first_season_id'] if pd.notna(row['first_season_id']) else None
                    last_season_id = row['last_season_id'] if pd.notna(row['last_season_id']) else None
                    
                    cursor.execute(f"SELECT franchise_id FROM {franchise_table_name} WHERE franchise_name_common = %s", (row['franchise_name_common'],))
                    existing_franchise = cursor.fetchone()

                    if existing_franchise:
                        new_franchise_id = existing_franchise[0]
                        logger.debug(f"Franchise '{row['franchise_name_common']}' already exists with ID {new_franchise_id}. Using existing.")
                        # Potentially update existing record here if needed
                        # update_query = f"""UPDATE {franchise_table_name} 
                        #                     SET first_season_id = %s, last_season_id = %s, notes = %s, updated_at = CURRENT_TIMESTAMP 
                        #                     WHERE franchise_id = %s
                        #                     AND (COALESCE(first_season_id, -1) != COALESCE(%s, -1) 
                        #                          OR COALESCE(last_season_id, -1) != COALESCE(%s, -1) 
                        #                          OR COALESCE(notes, '') != COALESCE(%s, ''));"""
                        # cursor.execute(update_query, (first_season_id, last_season_id, row['notes'], new_franchise_id, first_season_id, last_season_id, row['notes']))
                    else:
                        insert_query = f"""
                            INSERT INTO {franchise_table_name} (franchise_name_common, first_season_id, last_season_id, notes)
                            VALUES (%s, %s, %s, %s)
                            RETURNING franchise_id;
                        """
                        cursor.execute(insert_query, (
                            row['franchise_name_common'],
                            first_season_id,
                            last_season_id,
                            row['notes']
                        ))
                        new_franchise_id = cursor.fetchone()[0]
                        franchises_loaded_count += 1
                    
                    source_franchise_id_map[row['source_franchise_id']] = new_franchise_id
            
            # Commit the transaction
            self.db_manager.conn.commit()
            logger.info(f"Processed {len(franchise_df_for_load)} franchises, inserted {franchises_loaded_count} new franchises into {franchise_table_name}.")

        except Exception as e:
            # Rollback the transaction if needed
            if self.db_manager.conn and not self.db_manager.conn.closed:
                self.db_manager.conn.rollback()
            logger.error(f"Error loading franchises into {franchise_table_name}: {e}", exc_info=True)
            return {'franchises_loaded': 0, 'teams_loaded': 0}
        
        if teams_df.empty:
            logger.info("No team data to load.")
            return {'franchises_loaded': franchises_loaded_count, 'teams_loaded': 0}

        if 'source_franchise_id' not in teams_df.columns:
            logger.error("'source_franchise_id' column missing in teams_df. Cannot map to new franchise IDs.")
            return {'franchises_loaded': franchises_loaded_count, 'teams_loaded': 0}
            
        teams_df['franchise_id'] = teams_df['source_franchise_id'].map(source_franchise_id_map)
        
        original_team_count = len(teams_df)
        teams_df.dropna(subset=['franchise_id'], inplace=True)
        if not teams_df.empty:
            teams_df['franchise_id'] = teams_df['franchise_id'].astype(int)
        
        if len(teams_df) < original_team_count:
            logger.warning(f"{original_team_count - len(teams_df)} teams dropped due to unmapped franchise_id.")

        if teams_df.empty:
            logger.info("No teams left to load after franchise ID mapping.")
            return {'franchises_loaded': franchises_loaded_count, 'teams_loaded': 0}
        
        team_cols_for_load = [
            'franchise_id', 'season_id', 'league_id', 'team_name_full', 
            'team_abbreviation', 'city', 'state_province', 'country', 'notes',
            'arena_name', 'arena_capacity', 'owner_name', 
            'general_manager_name', 'head_coach_name', 'd_league_affiliation_name'
        ]
        
        final_team_cols = [col for col in team_cols_for_load if col in teams_df.columns]
        teams_df_for_load = teams_df[final_team_cols].copy()

        for col in team_cols_for_load: # Ensure all target columns exist, even if with Nones
            if col not in teams_df_for_load.columns:
                teams_df_for_load[col] = None

        teams_loaded_count = self.load(
            teams_df_for_load, 
            team_table_name, 
            if_exists='append', 
            unique_constraint_cols=['franchise_id', 'season_id'] 
        )
        
        return {'franchises_loaded': franchises_loaded_count, 'teams_loaded': teams_loaded_count}


class TableMappingLoader(BaseLoader):
    """
    Loader that maps source data to multiple target tables based on a mapping configuration.
    Useful for complex ETL processes where source data needs to be split across multiple tables.
    """
    
    def __init__(self, db_manager, table_mappings=None):
        """
        Initialize the mapping loader.
        
        Args:
            db_manager (DatabaseManager): Database manager instance.
            table_mappings (dict): Mapping from source tables to target tables.
                Format: {source_table: {target_table: {columns: [...], key_columns: [...]}}}
        """
        super().__init__(db_manager)
        self.table_mappings = table_mappings or {}
    
    def add_mapping(self, source_table, target_table, columns=None, key_columns=None):
        """
        Add a mapping from source table to target table.
        
        Args:
            source_table (str): Name of the source table.
            target_table (str): Name of the target table.
            columns (list): List of columns to include. If None, include all columns.
            key_columns (list): List of columns that form a unique constraint.
        """
        if source_table not in self.table_mappings:
            self.table_mappings[source_table] = {}
        
        self.table_mappings[source_table][target_table] = {
            'columns': columns,
            'key_columns': key_columns
        }
    
    def load(self, data, source_table, if_exists='append', chunksize=1000):
        """
        Load data into multiple target tables based on mappings.
        
        Args:
            data (pandas.DataFrame): Data to load.
            source_table (str): Name of the source table.
            if_exists (str): {'append', 'replace', 'fail'}. Default 'append'.
            chunksize (int): Number of rows to insert at once. Default 1000.
            
        Returns:
            dict: Dictionary of {target_table: rows_loaded}.
        """
        if data.empty:
            logger.info(f"No data to load from source table '{source_table}'")
            return {}
        
        if source_table not in self.table_mappings:
            logger.warning(f"No mappings defined for source table '{source_table}'")
            return {}
        
        results = {}
        
        for target_table, mapping in self.table_mappings[source_table].items():
            columns = mapping.get('columns')
            key_columns = mapping.get('key_columns')
            
            # Select columns for target table
            if columns:
                # Only include columns that exist in the DataFrame
                existing_columns = [col for col in columns if col in data.columns]
                if len(existing_columns) != len(columns):
                    missing_columns = set(columns) - set(existing_columns)
                    logger.warning(f"Missing columns for target table '{target_table}': {missing_columns}")
                
                if not existing_columns:
                    logger.warning(f"No matching columns for target table '{target_table}'")
                    results[target_table] = 0
                    continue
                
                target_df = data[existing_columns].copy()
            else:
                target_df = data.copy()
            
            logger.info(f"Loading {len(target_df)} rows into target table '{target_table}'")
            
            try:
                # Use the PostgreSQLLoader to load the data
                loader = PostgreSQLLoader(self.db_manager)
                rows_loaded = loader.load(
                    data=target_df,
                    table_name=target_table,
                    if_exists=if_exists,
                    chunksize=chunksize,
                    unique_constraint_cols=key_columns
                )
                
                results[target_table] = rows_loaded
                
            except Exception as e:
                logger.error(f"Error loading data into target table '{target_table}': {str(e)}", exc_info=True)
                results[target_table] = 0
        
        return results


class IncrementalLoader(BaseLoader):
    """
    Loader for incremental loading of data based on a timestamp or sequence column.
    Only loads new or updated records since the last load.
    """
    
    def __init__(self, db_manager, tracking_table='etl_tracking'):
        """
        Initialize the incremental loader.
        
        Args:
            db_manager (DatabaseManager): Database manager instance.
            tracking_table (str): Name of the table to track last loaded timestamps/sequences.
        """
        super().__init__(db_manager)
        self.tracking_table = tracking_table
        self._ensure_tracking_table()
    
    def _ensure_tracking_table(self):
        """Ensure that the tracking table exists."""
        query = f"""
        CREATE TABLE IF NOT EXISTS {self.tracking_table} (
            table_name TEXT PRIMARY KEY,
            last_timestamp TIMESTAMP,
            last_sequence_value BIGINT,
            last_loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            rows_loaded BIGINT DEFAULT 0
        )
        """
        try:
            self.db_manager.execute_query(query)
            logger.info(f"Ensured tracking table '{self.tracking_table}' exists")
        except Exception as e:
            logger.error(f"Error creating tracking table '{self.tracking_table}': {str(e)}", exc_info=True)
    
    def get_last_loaded_value(self, table_name, column_type='timestamp'):
        """
        Get the last loaded timestamp or sequence value for a table.
        
        Args:
            table_name (str): Name of the table.
            column_type (str): Type of tracking column ('timestamp' or 'sequence').
            
        Returns:
            object: Last loaded value, or None if no previous loads.
        """
        value_column = 'last_timestamp' if column_type == 'timestamp' else 'last_sequence_value'
        
        query = f"""
        SELECT {value_column} FROM {self.tracking_table}
        WHERE table_name = %s
        """
        
        try:
            result = self.db_manager.execute_query(query, (table_name,), fetch_mode='one')
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Error getting last loaded value for table '{table_name}': {str(e)}", exc_info=True)
            return None
    
    def update_tracking(self, table_name, last_value, column_type='timestamp', rows_loaded=0):
        """
        Update the tracking information for a table.
        
        Args:
            table_name (str): Name of the table.
            last_value: Last timestamp or sequence value.
            column_type (str): Type of tracking column ('timestamp' or 'sequence').
            rows_loaded (int): Number of rows loaded in this batch.
        """
        value_column = 'last_timestamp' if column_type == 'timestamp' else 'last_sequence_value'
        null_column = 'last_sequence_value' if column_type == 'timestamp' else 'last_timestamp'
        
        query = f"""
        INSERT INTO {self.tracking_table} (table_name, {value_column}, {null_column}, last_loaded_at, rows_loaded)
        VALUES (%s, %s, NULL, CURRENT_TIMESTAMP, %s)
        ON CONFLICT (table_name) DO UPDATE
        SET {value_column} = %s,
            last_loaded_at = CURRENT_TIMESTAMP,
            rows_loaded = {self.tracking_table}.rows_loaded + %s
        """
        
        try:
            self.db_manager.execute_query(query, (table_name, last_value, rows_loaded, last_value, rows_loaded))
            logger.info(f"Updated tracking for table '{table_name}' with last {column_type} value: {last_value}")
        except Exception as e:
            logger.error(f"Error updating tracking for table '{table_name}': {str(e)}", exc_info=True)
    
    def load(self, data, table_name, timestamp_col=None, sequence_col=None, if_exists='append', chunksize=1000, unique_constraint_cols=None):
        """
        Incrementally load data into the database.
        
        Args:
            data (pandas.DataFrame): Data to load.
            table_name (str): Name of the target table.
            timestamp_col (str): Name of the timestamp column for incremental loading.
            sequence_col (str): Name of the sequence column for incremental loading.
            if_exists (str): {'append', 'replace', 'fail'}. Default 'append'.
            chunksize (int): Number of rows to insert at once. Default 1000.
            unique_constraint_cols (list): List of columns that form a unique constraint.
                
        Returns:
            int: Number of rows loaded.
        """
        if data.empty:
            logger.info(f"No data to load into table '{table_name}'")
            return 0
        
        # Determine tracking column and type
        if timestamp_col:
            tracking_col = timestamp_col
            column_type = 'timestamp'
        elif sequence_col:
            tracking_col = sequence_col
            column_type = 'sequence'
        else:
            logger.warning(f"No timestamp or sequence column specified for incremental loading of table '{table_name}'")
            # Fall back to regular loading
            loader = PostgreSQLLoader(self.db_manager)
            return loader.load(data, table_name, if_exists, chunksize, unique_constraint_cols)
        
        # Get last loaded value
        last_value = self.get_last_loaded_value(table_name, column_type)
        
        # Filter data for incremental load if we have a last value
        if last_value is not None:
            logger.info(f"Filtering data for incremental load of table '{table_name}' with last {column_type} value: {last_value}")
            filtered_data = data[data[tracking_col] > last_value].copy()
            
            if filtered_data.empty:
                logger.info(f"No new data to load into table '{table_name}'")
                return 0
            
            logger.info(f"Found {len(filtered_data)} new rows to load into table '{table_name}'")
            data_to_load = filtered_data
        else:
            logger.info(f"No previous load found for table '{table_name}', loading all data")
            data_to_load = data
        
        # Load the data
        loader = PostgreSQLLoader(self.db_manager)
        rows_loaded = loader.load(data_to_load, table_name, if_exists, chunksize, unique_constraint_cols)
        
        # Update tracking information
        if rows_loaded > 0:
            # Get the maximum value of the tracking column
            max_value = data_to_load[tracking_col].max()
            self.update_tracking(table_name, max_value, column_type, rows_loaded)
        
        return rows_loaded
