CREATE ROLE "user" LOGIN PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE nba_data_db TO "user";
ALTER DATABASE nba_data_db OWNER TO "user";

-- As before, ensure your Query Tool is connected to the 'nba_data_db' database 
-- (you might need to refresh your database list in pgAdmin and open a new query tool for it)
-- OR that you are connected as 'postgres' which has rights to grant on any public schema.
GRANT USAGE, CREATE ON SCHEMA public TO "user";
