# Security & Reliability

## Security by Design
- **Input Validation:** Rigorously validate and sanitize ALL external inputs (user input, API responses, file contents). Check for type, format, range, and potential malicious patterns.
- **Authentication & Authorization:** Where applicable, consider how authentication and authorization should be handled for the feature being implemented. Do not hardcode credentials.
- **Least Privilege:** Design components to operate with the minimum necessary permissions.
- **Secure Dependencies:** Be mindful of introducing new dependencies; use established, well-maintained libraries where possible. (Note: Cline cannot scan dependencies).
- **Data Protection:** Consider if sensitive data is being handled. Suggest appropriate handling (e.g., avoiding logging sensitive data, considering encryption needs if applicable).

## Reliability
- **Error Handling:** Implement robust error handling as defined in `02-code-quality.md`.
- **Fault Tolerance:** Consider potential failure points (e.g., network calls, database access) and suggest appropriate retry mechanisms or fallback strategies if necessary.
- **Resource Management:** Ensure resources (file handles, network connections, etc.) are properly managed and released.