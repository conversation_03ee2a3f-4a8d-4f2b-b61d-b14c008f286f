# Tech Context

## Technologies Used
*   **Programming Language:** Python 3.9+ (Confirmed: Python 3.10.11 installed and active via venv).
*   **Database:** PostgreSQL (Version 17) (Setup previously completed).
*   **Data Collection Libraries:**
    *   `nba_api`: For accessing NBA.com official stats API.
    *   `basketball_reference_web_scraper`: For accessing Basketball-Reference.com data.
    *   `requests`: For general HTTP requests (custom scrapers).
    *   `BeautifulSoup4`: For HTML parsing (custom scrapers).
    *   `Scrapy`: Potentially for more complex custom scraping tasks.
*   **Data Processing Libraries:**
    *   `pandas`: Core library for data manipulation and analysis.
    *   `numpy`: For numerical operations, often used with pandas.
    *   `fuzzywuzzy`: Potentially for string matching in name standardization.
*   **Python-DB Connector:** `psycopg2-binary`
*   **Configuration Management:** `.ini` files (`config.ini`) and `.env` files (`python-dotenv` library). (Confirmed: `config.ini` updated, `.env` setup guided).
*   **Version Control:** Git (Confirmed: version 2.49.0.windows.1 installed), GitHub/GitLab (for personal backup/versioning).

## Development Setup
*   **Operating System:** User's choice (Windows 11 confirmed for this session). Instructions assume compatibility.
*   **Python Environment:** Python 3.10.11 installed and confirmed. Project uses an existing virtual environment named `venv` (located at `c:/Users/<USER>/Projects/nbadb/Database/venv`), which has been activated by the user.
*   **Dependencies Installation:** Confirmed: All dependencies listed in `requirements.txt` are satisfied/installed within the `venv`.
*   **Database Server:** Local PostgreSQL instance installed and running (Setup previously completed).
*   **Database Client/IDE:** DBeaver Community Edition (recommended) or `psql` for database management and querying (DBeaver setup previously completed).
*   **Code Editor/IDE:** VS Code or PyCharm Community Edition. Guidance provided to configure IDE with the project's `venv` interpreter.
*   **Project Directory Structure:** Current project root is `c:/Users/<USER>/Projects/nbadb/Database`. Structure includes `data/`, `database/`, `db/`, `etl/`, `logs/`.
*   **Initial Setup Steps (Status):**
    1.  Install Python and Git. (Confirmed Complete)
    2.  Set up project directory and initialize Git. (Project directory confirmed; `.git` folder exists, implying initialization).
    3.  Create and activate Python virtual environment. (Existing `venv` confirmed and activated by user).
    4.  Install Python libraries from `requirements.txt`. (Confirmed: Libraries satisfied/installed in `venv`).
    5.  Install and configure PostgreSQL server, create database and user. (Confirmed Complete - previous task).
    6.  Set up `config.ini` and `.env` files. (Confirmed Complete: `config.ini` updated with project-specifics and placeholders; `.env` setup guided, confirmed in `.gitignore`, and user confirmed local setup).
    7.  Install DBeaver. (Confirmed Complete - previous task).

## Technical Constraints
*   **Free and Open Source:** All software and tools used must be free and open-source.
*   **Rate Limiting/Politeness:** Strict adherence to API rate limits and website `robots.txt` / Terms of Service is required. Configurable delays between requests are mandatory for all external data sources.
*   **Data Availability:** The project is constrained by the data made available by the chosen free sources. Some historical data may be incomplete or missing.
*   **Scalability (Personal Project):** While good practices are followed, the system is primarily designed for a single-user, local setup rather than large-scale, multi-user production deployment. Performance optimizations will be considered but are secondary to data completeness and correctness for MVP.
*   **Maintenance:** Custom web scrapers are prone to breaking if website structures change, requiring ongoing maintenance.

## Dependencies
*   **Core Python Libraries (from `requirements.txt` in `planning/build.md`):**
    *   `pandas`
    *   `numpy`
    *   `requests`
    *   `beautifulsoup4`
    *   `Scrapy`
    *   `psycopg2-binary`
    *   `nba-api`
    *   `basketball-reference-web-scraper`
    *   `python-dotenv`
    *   `sqlalchemy` (noted as potentially useful)
*   **External Software:**
    *   Python 3.9+ interpreter
    *   Git
    *   PostgreSQL Server
*   **Data Sources (Implicit Dependencies):**
    *   Kaggle datasets (must be downloaded locally into `data/kaggle_datasets/`).
    *   Accessibility and stability of NBA.com API.
    *   Accessibility and stability of Basketball-Reference.com.
    *   Accessibility and stability of Spotrac.com.

## Tool Usage Patterns
*   **`etl/run_etl.py`:** Command-line entry point for the ETL pipeline. Parses arguments, sets up logging, and initiates the `ETLOrchestrator`.
*   **`etl/etl_orchestrator.py`:** Central orchestrator script. Manages the overall ETL workflow, including schema initialization, data source processing, and coordination of extractors, transformers, and loaders.
*   **`etl/extractors.py`:** Contains classes for extracting data from various sources (e.g., `KaggleCSVExtractor`, `NBAAPIExtractor`, `WyattKaggleCSVExtractor`).
*   **`etl/transformers.py`:** Contains classes for transforming extracted data (e.g., `KaggleTransformer`, `NBAAPITransformer`, `WyattFranchiseTeamTransformer`). Some transformation logic also resides within `etl_orchestrator.py`.
*   **`etl/loaders.py`:** Contains classes for loading transformed data into the database (e.g., `PostgreSQLLoader`).
*   **`etl/schema_utils.py`:** Provides utilities for interacting with and validating database schemas.
*   **`database/db_manager.py`:** Handles all direct interactions with PostgreSQL, including DDL execution and data loading (upserts).
*   **`database/schema_ddl/`:** Contains all SQL scripts for creating the database schema, executed in numerical order.
*   **`config.ini` & `.env`:** Used by all relevant modules to fetch configuration parameters and sensitive credentials.
*   **`logs/`:** Directory for storing ETL log files (e.g., `etl_TIMESTAMP.log`, `etl.log`).
*   **`docs/`:** Contains all project documentation, with `data_dictionary.md` being a key reference for schema details.
*   **`tests/`:** (Planned) Contains unit and integration tests, organized by the module they are testing.

## MCP Servers & Tooling

*   **MCP Client Configuration:** Managed via `cline_mcp_settings.json` located at `c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\saoudrizwan.claude-dev\settings\cline_mcp_settings.json`. This file defines how MCP servers are launched and managed by the client application.
*   **Currently Configured Servers:**
    *   **PostgreSQL MCP Server (`@modelcontextprotocol/server-postgres`):**
        *   **Purpose:** Provides read-only access to the project's `nba_data_db` PostgreSQL database.
        *   **Setup:** Configured in `cline_mcp_settings.json` to run via `npx` (using the full path to `npx-cli.js` with `node`).
        *   **Configuration Snippet (from `cline_mcp_settings.json`):**
            ```json
            "github.com/modelcontextprotocol/servers/tree/main/src/postgres": {
              "type": "stdio",
              "command": "node",
              "args": [
                "C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npx-cli.js",
                "-y",
                "@modelcontextprotocol/server-postgres",
                "postgresql://user:password@localhost:5432/nba_data_db"
              ],
              "env": {}, "disabled": false, "autoApprove": [], "alwaysAllow": []
            }
            ```
    *   Other default servers available: `context7-mcp`, `perplexity-mcp`, `sequentialthinking`.
*   **MCP Server Discovery Resource:**
    *   A curated list of 50 potentially useful MCP servers and development tools for this project is documented in `planning/nbadb/mcplist.md`. This serves as a reference for future MCP server integrations.
*   **MCP Development Tools (Not run as persistent servers but used for development):**
    *   `@modelcontextprotocol/inspector`: A CLI tool for debugging MCP servers.
    *   `@modelcontextprotocol/create-python-server`: CLI tool to bootstrap new Python MCP servers.
    *   `@modelcontextprotocol/create-typescript-server`: CLI tool to bootstrap new TypeScript MCP servers.
