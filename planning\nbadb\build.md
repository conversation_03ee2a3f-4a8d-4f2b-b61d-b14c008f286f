
**Overall Goal for this Phase:** To transition from planning and design to the actual development and execution of the data collection system, ETL pipeline, and database population.

---

### 1. Project Setup Checklist (Environment & Database)

This checklist is primarily based on Section VI.A & B of your plan.

**A. Local Development Environment Setup:**

* [ ] **Install Python:**
  * [ ] Verify Python 3.9+ is installed (`python --version` or `python3 --version`).
  * [ ] If not, download and install from [python.org](https://python.org). Ensure it's added to your system PATH.
* [ ] **Install Git:**
  * [ ] Verify Git is installed (`git --version`).
  * [ ] If not, download and install from [git-scm.com](https://git-scm.com/).
* [ ] **Set up Project Directory:**
  * [ ] Create the main project folder (e.g., `nba_historical_data_project/`).
  * [ ] Navigate into the project folder in your terminal/command prompt.
* [ ] **Initialize Git Repository:**
  * [ ] `git init`
  * [ ] (Optional, but recommended) Create an initial `.gitignore` file (see proposed file structure in Phase 2 for common entries like `venv/`, `__pycache__/`, `*.log`, `.env`).
  * [ ] (Optional) Create a remote repository on GitHub/GitLab and link it.
* [ ] **Create and Activate Python Virtual Environment:**
  * [ ] `python -m venv venv` (or `python3 -m venv venv`)
  * [ ] Activate the virtual environment:
    * Windows: `.\venv\Scripts\activate`
    * macOS/Linux: `source venv/bin/activate`
    * (Your terminal prompt should change to indicate the active venv).
* [ ] **Install Python Libraries:**
  * [ ] Create a `requirements.txt` file with the following content:

        ```
        pandas
        numpy
        requests
        beautifulsoup4
        Scrapy
        psycopg2-binary
        nba-api
        basketball-reference-web-scraper
        python-dotenv  # For managing .env files
        # Add other utility libraries as identified, e.g., fuzzywuzzy if used for name standardization
        # sqlalchemy # Potentially useful with pandas.to_sql for more complex DB interactions
        ```

  * [ ] Install libraries: `pip install -r requirements.txt`
* [ ] **Install Code Editor/IDE:**
  * [ ] Install VS Code ([code.visualstudio.com](https://code.visualstudio.com/)) or PyCharm Community Edition.
  * [ ] Configure your IDE to use the project's virtual environment interpreter.
  * [ ] Install relevant Python extensions in your IDE.
* [ ] **Set up Configuration Files:**
  * [ ] Create `config.ini` (for non-sensitive settings).
  * [ ] Create `.env` (for sensitive credentials like DB password) and add it to `.gitignore`.
    * Example `.env` content:

            ```ini
            DB_HOST=localhost
            DB_PORT=5432
            DB_NAME=nba_data_db
            DB_USER=your_db_user
            DB_PASSWORD=your_secure_password
            ```

**B. Database Setup (PostgreSQL):**

* [ ] **Install PostgreSQL:**
  * [ ] Download and install PostgreSQL (e.g., from [postgresql.org](https://www.postgresql.org/download/)).
  * [ ] During installation, set a secure password for the default `postgres` superuser.
  * [ ] Ensure PostgreSQL services are running.
* [ ] **Create Database and Dedicated User (using `psql` or a GUI like pgAdmin/DBeaver connected as `postgres` user):**
  * [ ] Connect to PostgreSQL as the `postgres` user.
  * [ ] Create the database: `CREATE DATABASE nba_data_db;`
  * [ ] Create a dedicated user: `CREATE USER your_db_user WITH PASSWORD 'your_secure_password';` (Replace `your_db_user` and `your_secure_password` with your choices – match `.env`).
  * [ ] Grant privileges to the user on the database: `GRANT ALL PRIVILEGES ON DATABASE nba_data_db TO your_db_user;`
  * [ ] (Optional, good practice) Alter the user to be the owner of the database if they are the primary operator: `ALTER DATABASE nba_data_db OWNER TO your_db_user;`
  * [ ] (If using schemas other than `public`): `GRANT USAGE, CREATE ON SCHEMA public TO your_db_user;` (or your specific schema).
* [ ] **Install and Configure DBeaver Community Edition:**
  * [ ] Download and install DBeaver ([dbeaver.io](https://dbeaver.io/)).
  * [ ] Create a new database connection in DBeaver:
    * Select PostgreSQL.
    * Host: `localhost` (or as configured).
    * Port: `5432` (or as configured).
    * Database: `nba_data_db`.
    * User: `your_db_user` (from `.env`).
    * Password: `your_secure_password` (from `.env`).
  * [ ] Test the connection.

---

### 2. Schema Implementation Checklist

Based on Section VI.C and the file structure from Phase 2.

* [ ] **Prepare DDL Scripts Directory:**
  * [ ] Create the directory `database/schema_ddl/`.
* [ ] **Write SQL DDL Scripts (Iterative Process):**
  * For each group of related tables, create a numbered SQL file (e.g., `001_create_leagues_seasons.sql`).
  * [ ] **Script 1 (`001_create_leagues_seasons.sql`):**
    * [ ] `CREATE TABLE Leagues (...)`
    * [ ] `CREATE TABLE Seasons (...)` (with FK to Leagues)
  * [ ] **Script 2 (`002_create_franchises_teams.sql`):**
    * [ ] `CREATE TABLE Franchises (...)`
    * [ ] `CREATE TABLE Teams (...)` (with FK to Franchises, Leagues)
    * [ ] `CREATE TABLE TeamSeason (...)` (with FKs to Teams, Seasons)
  * [ ] **Script 3 (`003_create_players_personnel.sql`):**
    * [ ] `CREATE TABLE Players (...)`
    * [ ] `CREATE TABLE PlayerNicknames (...)` (FK to Players)
    * [ ] `CREATE TABLE PlayerRelatives (...)` (FK to Players)
    * [ ] `CREATE TABLE PlayerSocialMedia (...)` (FK to Players)
    * [ ] `CREATE TABLE PlayerLinks (...)` (FK to Players)
    * [ ] `CREATE TABLE PlayerNameTranslations (...)` (FK to Players)
    * [ ] `CREATE TABLE PlayerSeasonRoster (...)` (FKs to Players, TeamSeason or Teams & Seasons)
    * [ ] `CREATE TABLE Personnel (...)` (Coaches, Staff - if distinct from Players)
  * [ ] **Script 4 (`004_create_games_stats.sql`):**
    * [ ] `CREATE TABLE Games (...)` (FKs to Seasons, Teams for home/away)
    * [ ] `CREATE TABLE PlayerGameStats (...)` (FKs to Players, Games, Teams)
    * [ ] `CREATE TABLE TeamGameStats (...)` (FKs to Teams, Games)
    * [ ] `CREATE TABLE PlayerSeasonTotals (...)` (FKs to Players, Seasons, Teams)
    * [ ] `CREATE TABLE PlayByPlayEvents (...)` (FK to Games, Players - optional for MVP if too complex initially)
    * [ ] `CREATE TABLE LineupStats (...)` (FK to Games, Teams - optional for MVP)
  * [ ] **Script 5 (`005_create_awards_draft_transactions.sql`):**
    * [ ] `CREATE TABLE Awards (...)` (FK to Leagues)
    * [ ] `CREATE TABLE PlayerAwards (...)` (FKs to Players, Awards, Seasons, Teams)
    * [ ] `CREATE TABLE TeamAwards (...)` (FKs to Teams, Awards, Seasons)
    * [ ] `CREATE TABLE Transactions (...)`
    * [ ] `CREATE TABLE TransactionPlayersInvolved (...)` (FKs to Transactions, Players, Teams - for 'from'/'to' teams)
    * [ ] `CREATE TABLE Drafts (...)` (FK to Seasons)
    * [ ] `CREATE TABLE DraftPicks (...)` (FKs to Drafts, Players, Teams)
  * [ ] **Script 6 (`006_create_contracts_salaries.sql`):**
    * [ ] `CREATE TABLE Contracts (...)` (FKs to Players, Teams)
    * [ ] `CREATE TABLE PlayerSalaries (...)` (FKs to Contracts or Players/Seasons, amount, year)
  * [ ] **Script 7 (`007_create_referees_hof_misc.sql`):**
    * [ ] `CREATE TABLE Referees (...)`
    * [ ] `CREATE TABLE RefereeGameAssignments (...)` (FKs to Referees, Games)
    * [ ] `CREATE TABLE StatDefinitions (...)`
    * [ ] `CREATE TABLE HoFProbMethodology (...)`
  * [ ] **Script 8 (`008_create_indexes.sql`):**
    * [ ] `CREATE INDEX ... ON ...` for all FKs not automatically indexed by PK.
    * [ ] `CREATE INDEX ... ON ...` for frequently queried columns (e.g., `Players(last_name, first_name)`, `Games(game_date)`).
  * [ ] **Script 9 (`009_create_mapping_review_tables.sql`):**
    * [ ] `CREATE TABLE player_identifier_mapping (...)`
    * [ ] `CREATE TABLE team_identifier_mapping (...)`
    * [ ] `CREATE TABLE unmatched_player_names_review (...)`
    * [ ] `CREATE TABLE data_source_conflicts (...)`
* [ ] **Develop DDL Execution Logic (`database/db_manager.py`):**
  * [ ] Implement `setup_schema(config, ddl_scripts_path)` function to read and execute SQL files in numerical order.
* [ ] **Execute DDL Scripts:**
  * [ ] Run `python main_etl.py --setup-schema-only` (assuming you add this CLI flag to `main_etl.py` to call `setup_schema`).
  * [ ] OR, manually execute scripts via DBeaver in the correct order.
  * [ ] Verify all tables, columns, constraints, and indexes are created correctly in DBeaver.
* [ ] **Commit DDL Scripts to Git:**
  * [ ] `git add database/schema_ddl/*.sql`
  * [ ] `git commit -m "feat: Implement initial database schema DDL scripts"`

---

### 3. ETL Pipeline Development Roadmap (Feature Implementation Order)

Based on Section VI.D and logical dependencies. Develop modules according to the file structure defined in Phase 2.

1. **Core Database Utilities (`database/db_manager.py`, `database/db_utils.py`):**
    * Implement database connection pooling/management.
    * Implement generic data loading functions (e.g., `bulk_upsert_df`) that will be used by multiple collection modules. Test these thoroughly.
2. **Configuration Loading (`config.py` or utils):**
    * Implement logic to load `config.ini` and `.env` files.
3. **Logging Setup (in `main_etl.py` or a central logging utility):**
    * Configure Python's `logging` module as per Phase 3 style guide.
4. **Module 1: Kaggle Dataset Ingestion (`data_collection/kaggle_ingestor.py`):**
    * **Dependency:** Core DB utilities for loading.
    * Focus on reading CSVs/SQLite, basic mapping, and initial bulk load.
5. **Static Data for APIs (within `nba_api_client.py` and `bball_ref_client.py`):**
    * Fetch and store static lists like players and teams from `nba_api` (e.g., `get_players()`, `get_teams()`). These often provide canonical IDs needed by other processes.
    * Store these in `Players` and `Teams` tables. This might involve some initial name standardization.
6. **Module 5 (Partial - Initial Name Standardization Logic in `data_processing/name_standardizer.py`):**
    * Develop basic functions for player/team name standardization using the static lists from step 5 as the initial canonical source. This will be refined later but is needed early.
7. **Module 2: `nba_api` Client Scripts (`data_collection/nba_api_client.py`):**
    * **Dependency:** Core DB utilities, static player/team IDs, initial name standardization.
    * Start with a few key endpoints (e.g., player game logs, season stats for a limited set of seasons/players).
8. **Module 3: `basketball_reference_web_scraper` Client Scripts (`data_collection/bball_ref_client.py`):**
    * **Dependency:** Core DB utilities, potentially player names/IDs for cross-referencing, initial name standardization.
    * Focus on fetching data types that complement `nba_api`, especially historical BAA/NBL/ABA.
9. **Module 4: Custom Web Scraping Scripts (Targeted - `data_collection/custom_scrapers/`):**
    * **Dependency:** Core DB utilities, potentially IDs/names from other modules to form URLs.
    * Tackle one specific target at a time (e.g., Spotrac contracts for a few players). These are often independent.
10. **Module 5: Data Cleaning and Transformation Layer (Full Implementation - `data_processing/`):**
    * **Dependency:** Data being collected by Modules 1-4.
    * Implement comprehensive cleaning, type conversion, conflict resolution, and advanced name standardization logic. This module will take dataframes from collection modules and prepare them for final loading.
11. **Module 6: Data Loading and ETL Orchestration (`main_etl.py`):**
    * **Dependency:** All other modules.
    * Develop the master script to call collection, processing, and loading functions in sequence. Implement run modes (`--full-historical-load`, `--daily-update`, etc.).
12. **Foundational Documentation (MVP Versions - `README.md`, `docs/data_dictionary.md`):**
    * Develop these iteratively as the schema and pipeline solidify.

---

### 4. Task Breakdown Examples (for 1-2 ETL modules)

**For "Module 1: Kaggle Dataset Ingestion" (`data_collection/kaggle_ingestor.py`):**

* [ ] **Task:** Define file paths for Kaggle CSVs and SQLite DB in `config.ini`.
* [ ] **Task:** Write `load_config()` function to access these paths.
* [ ] **Task:** Implement `read_kaggle_csv(file_path)` function using `pandas.read_csv()`.
* [ ] **Task:** Implement `read_kaggle_sqlite_table(db_path, table_name)` function using `sqlite3` and `pandas.read_sql_query()`.
* [ ] **Task:** For each relevant Kaggle file/table (e.g., games, player stats):
  * [ ] **Sub-Task:** Define column mapping dictionary (Kaggle name -> Your DB schema name).
  * [ ] **Sub-Task:** Implement transformation function: takes Kaggle DataFrame, applies column mapping, performs basic data type conversions (`pd.to_datetime`, `pd.to_numeric`), handles obvious null placeholders.
  * [ ] **Sub-Task:** Test transformation function with sample data.
* [ ] **Task:** Implement `load_df_to_postgres(df, table_name, db_conn_params)` function in `db_manager.py` (if not already done) using `psycopg2` (e.g., `extras.execute_values` or `io.StringIO` with `COPY FROM STDIN`).
* [ ] **Task:** Write main function in `kaggle_ingestor.py` to orchestrate reading, transforming, and loading for all relevant Kaggle data.
* [ ] **Task:** Add logging (records read, records loaded, errors) using the central logging setup.
* [ ] **Task:** Test entire module by running it and verifying data in PostgreSQL.
* [ ] **Task:** Commit to Git.

**For "Module 2: `nba_api` Client Scripts" (`data_collection/nba_api_client.py`):**

* [ ] **Task:** Identify 2-3 core `nba_api` endpoints for initial implementation (e.g., `stats.static.players.get_players()`, `stats.endpoints.PlayerGameLog`).
* [ ] **Task:** Implement `fetch_static_players()`:
  * [ ] Calls `nba_api.stats.static.players.get_players()`.
  * [ ] Transforms response DataFrame to match `Players` table schema (basic info).
  * [ ] Uses `db_manager.py` to upsert player data into `Players` table.
  * [ ] Add logging.
* [ ] **Task:** Implement `fetch_player_game_logs(player_id, season_year_str)`:
  * [ ] Takes `player_id` and season string (e.g., "2022-23").
  * [ ] Calls `nba_api.stats.endpoints.PlayerGameLog(player_id=..., season=...)`.
  * [ ] Implements delay from `config.ini` (`time.sleep()`).
  * [ ] Parses the response DataFrame.
  * [ ] Defines column mapping (API response -> `PlayerGameStats` schema).
  * [ ] Transforms data (types, mappings).
  * [ ] Returns transformed DataFrame.
* [ ] **Task:** Implement main orchestration function in `nba_api_client.py` (e.g., `run_nba_api_collection(target_seasons)`):
  * [ ] Gets list of players (e.g., from `Players` table or a limited test list).
  * [ ] Loops through players and target seasons.
  * [ ] Calls `fetch_player_game_logs()`.
  * [ ] Takes returned DataFrame and uses `db_manager.py` to load into `PlayerGameStats` (with upsert logic).
  * [ ] Implements robust error handling for API calls (retries, logging failures).
* [ ] **Task:** Add logging for API calls, data fetched, errors.
* [ ] **Task:** Test with a single player/season, verify data in `PlayerGameStats`.
* [ ] **Task:** Commit to Git.

---

### 5. Basic Testing Strategy

* **Unit Tests (Python `unittest` or `pytest` framework in `tests/` directory):**
  * **Focus:** Test individual functions in isolation, especially in `data_processing/` modules (e.g., `name_standardizer.py`, `cleaner.py`, `transformer.py`) and parsing/transformation logic within collection modules.
  * **Method:**
    * Use sample input data (small DataFrames, strings, JSON snippets stored in `tests/test_utils/sample_data/`).
    * Mock external dependencies:
      * Mock database connections/calls (`unittest.mock.patch` for `psycopg2` functions).
      * Mock API calls (`requests.get`, `nba_api` library calls) to return predefined sample responses.
      * Mock file system operations if reading local files.
    * Assert that function outputs match expected outputs for given inputs.
    * Test edge cases, error conditions, and different data variations.
  * **Example:** `test_name_standardizer.py` would have tests for `standardize_player_name()` with various name inputs.
* **Integration Tests (Can be manual scripts initially, or more formal tests):**
  * **Focus:** Test the interaction between modules or a complete flow for a small piece of data.
  * **Method:**
    * Test individual data collection scripts (e.g., `kaggle_ingestor.py`, `nba_api_client.py`) by running them for a very small, controlled data subset (e.g., one specific Kaggle file, one player for one season from `nba_api`).
    * These tests WILL hit the actual database (a test instance, or your dev DB if you clean up after).
    * Verify that data is fetched, processed (if applicable for that script), and loaded into the PostgreSQL database correctly.
    * Check data integrity in the DB (correct values, types, relationships for the test data).
  * **Example:** A script `tests/integration/test_full_pipeline_one_player.py` might orchestrate fetching, processing, and loading data for a single, hardcoded player through all relevant MVP modules.
* **Data Validation Checks (SQL queries or Python scripts run post-load):**
  * **Focus:** Check the quality, consistency, and integrity of the data in the database after ETL runs.
  * **Method:**
    * Write SQL queries (store in `docs/data_validation_queries.sql` or similar) to:
      * Check for `NULL` values in critical `NOT NULL` columns.
      * Verify referential integrity (e.g., all `player_id` in `PlayerGameStats` exist in `Players`). (DB constraints handle some of this, but custom checks can be useful).
      * Look for duplicate primary keys (should be prevented by constraints, but good to verify).
      * Check for statistical anomalies (e.g., points < 0, FG% > 100%, sum of player points in a game not matching team score).
      * Count records in key tables to ensure expected volumes.
      * Compare data against known values from source websites (spot-checking).
  * These can be run manually after ETL or integrated into the `main_etl.py` as a final step.
* **Source Comparison (Manual Spot-Checks):**
  * Regularly (especially after changes to scrapers/API clients) manually compare a few records in your database against the original source (NBA.com, Basketball-Reference.com) to ensure accuracy.

---

### 6. Automation & Maintenance Outline

Based on Section VI.D and general best practices.

**A. Automation of ETL Updates (Daily/Regular):**

* **Scheduling Tool:**
  * **Linux/macOS:** `cron`
  * **Windows:** Task Scheduler
* **Script to Schedule:** `main_etl.py` with appropriate arguments (e.g., `python /path/to/project/main_etl.py --run-mode daily_update --season CURRENT_SEASON_YEAR`).
* **Steps for Setup (Conceptual - `cron` example):**
    1. Ensure `main_etl.py` is executable or called via `python`.
    2. Open crontab for editing: `crontab -e`.
    3. Add a cron job entry. Example for daily run at 3:00 AM:

        ```cron
        0 3 * * * /path/to/your/venv/bin/python /path/to/nba_historical_data_project/main_etl.py --run-mode daily_update --season $(date +\%Y) >> /path/to/nba_historical_data_project/logs/cron_etl.log 2>&1
        ```

        * Replace paths.
        * `$(date +\%Y)` can dynamically set the current year for the season argument if your script expects just the start year. Adjust as needed for your season string format.
        * `>> ... 2>&1` redirects stdout and stderr to a log file.
* **Configuration for Updates:** Ensure `main_etl.py` and `config.ini` can correctly identify which data needs to be updated (e.g., only fetch games from yesterday onwards, only process current season data).

**B. Key Things to Monitor:**

* **Scraper Breakages:**
  * **Cause:** Website HTML structure changes (for custom scrapers and `basketball_reference_web_scraper`).
  * **Monitoring:**
    * Check ETL logs for parsing errors or a sudden drop in records fetched from scraped sources.
    * Implement "health checks" in scripts: if a scraper that usually returns data returns zero records for several consecutive runs, log a prominent ERROR or WARNING.
* **API Changes:**
  * **Cause:** `nba_api` endpoints might change, or the library itself might have breaking changes in new versions.
  * **Monitoring:**
    * ETL logs for API error codes (4xx, 5xx) or exceptions from the `nba_api` library.
    * Periodically check the `nba_api` GitHub repository for reported issues or updates.
* **Disk Space:** Monitor disk space on the machine hosting PostgreSQL and storing logs/backups.
* **ETL Job Success/Failure:** Check main ETL logs (`etl.log`, `cron_etl.log`) daily for successful completion or any errors.
* **Data Quality Metrics:** If you implement data validation checks that output metrics (e.g., % of names unstandardized), monitor these for unusual spikes.

**C. Strategy for Updating Libraries and Python Version:**

* **Libraries (`requirements.txt`):**
  * Pin versions initially for stability: `pandas==X.Y.Z`.
  * Periodically (e.g., quarterly or bi-annually), review key libraries for updates: `pip list --outdated`.
  * Before updating, check library changelogs for breaking changes.
  * Update in a development branch/environment first.
  * Re-run tests thoroughly after updates.
  * Update `requirements.txt` with new pinned versions.
* **Python Version:**
  * Upgrades are less frequent. Only upgrade if a new feature is needed or the current version becomes unsupported.
  * Test extensively in a separate environment before migrating the main project.

---

### 7. Troubleshooting Tips (Initial List)

* **Common Web Scraping Issues:**
  * **HTML Structure Changes:** Update CSS selectors / XPath expressions in `BeautifulSoup4`/`Scrapy` code. For `basketball_reference_web_scraper`, check for library updates or report issue.
  * **Rate Limiting/IP Blocks:**
    * Ensure polite delays (from `config.ini`) are respected and sufficiently long.
    * Rotate User-Agents (though less critical for personal, low-frequency scraping).
    * If blocked, wait a significant period before retrying.
    * For very persistent blocks (unlikely for this project's scale if polite), more advanced techniques like proxy rotation might be needed (outside MVP scope).
  * **JavaScript-Rendered Content (Minimized Use with Selenium):** If Selenium is used and fails, check if the WebDriver (e.g., ChromeDriver) is compatible with the browser version. Ensure target elements are loaded before trying to interact.
* **API Issues (`nba_api`):**
  * **Endpoint Changes/Deprecation:** Check `nba_api` library documentation/GitHub for updates.
  * **Rate Limits:** Ensure delays are implemented. The library might handle some, but explicit delays add robustness.
  * **Authentication Changes:** `nba_api` is generally unauthenticated. If this ever changes, library and code would need updates.
  * **Invalid Parameters:** Double-check parameters passed to API functions (season format, IDs).
* **Data Inconsistencies/Errors:**
  * Use data validation scripts/queries to flag anomalies.
  * Trace data lineage (using Data Dictionary sources) to identify the origin of the error.
  * Manually review flagged data in DBeaver.
  * Correct data at the source if possible, or implement cleaning rules in Module 5.
  * For systematic issues, adjust ETL logic.
* **PostgreSQL Performance:**
  * **Slow Queries:** Use `EXPLAIN ANALYZE your_query;` to understand the query plan.
  * **Missing Indexes:** Add indexes to columns frequently used in `WHERE`, `JOIN`, `ORDER BY`.
  * **Large Table Scans:** Optimize queries to use indexes.
  * **Database Bloat:** Periodically run `VACUUM` (especially `VACUUM FULL` with downtime, or rely on autovacuum).
  * (For very large datasets, consider partitioning - outside MVP scope).
* **ETL Script Failures:**
  * Check `etl.log` and module-specific logs for detailed error messages and tracebacks.
  * Isolate the failing module and test it with specific inputs.
  * Check for issues like incorrect file paths in `config.ini`, DB connection problems, or unexpected data formats from sources.

----
Remember to tackle development iteratively, commit your changes to Git frequently, and test at each stage.
