### 1. Data Dictionary Structure (Template for `docs/data_dictionary.md`)

This template should be used for each table in your PostgreSQL database. It will be a Markdown document.

```markdown
# Data Dictionary: NBA Historical Data Project

## Table: `[TableName]` (e.g., `Players`)

**Description:** [Briefly describe the purpose of this table and what kind of data it holds. e.g., "Stores biographical and identifying information for all players across NBA, BAA, NBL, and ABA history."]

| Column Name             | Data Type (PostgreSQL) | Description                                                                 | Source(s)                                                                    | Constraints                                     | Nullability | Units         | Examples                                  | Notes                                                                                                                               |
| :---------------------- | :--------------------- | :-------------------------------------------------------------------------- | :--------------------------------------------------------------------------- | :---------------------------------------------- | :---------- | :------------ | :---------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------- |
| `[column_name]`         | `[PG_DataType]`        | `[Detailed purpose of the column, what it represents]`                      | `[e.g., nba_api: commonallplayers; BRef: Player Page; Kaggle: players.csv]` | `[PK, FK -> other_table(column), UNIQUE, NOT NULL]` | `[YES/NO]`  | `[e.g., inches]` | `[e.g., 2544, 'LeBron James', '1984-12-30']` | `[e.g., Primary key for the table. Official NBA player ID if available. For historical players without official ID, a generated UUID.]` |
| `player_id`             | `INTEGER`              | Unique identifier for the player.                                           | `nba_api: commonallplayers (PERSON_ID)`                                      | `PK, NOT NULL`                                  | `NO`        | `N/A`         | `2544`                                    | `Serves as the canonical player identifier across the database.`                                                                    |
| `first_name`            | `VARCHAR(100)`         | Player's first name.                                                        | `nba_api, BRef, Kaggle`                                                      | `NOT NULL`                                      | `NO`        | `N/A`         | `'LeBron'`                                |                                                                                                                                     |
| `last_name`             | `VARCHAR(100)`         | Player's last name.                                                         | `nba_api, BRef, Kaggle`                                                      | `NOT NULL`                                      | `NO`        | `N/A`         | `'James'`                                 |                                                                                                                                     |
| `birth_date`            | `DATE`                 | Player's date of birth.                                                     | `nba_api, BRef`                                                              |                                                 | `YES`       | `YYYY-MM-DD`  | `'1984-12-30'`                            | `May be NULL for some very early historical players if data is unavailable.`                                                        |
| `height_inches`         | `INTEGER`              | Player's height in inches.                                                  | `nba_api (HEIGHT), BRef`                                                     | `CHECK (height_inches > 0)`                     | `YES`       | `inches`      | `81`                                      | `Original data might be in feet-inches; converted to total inches during ETL.`                                                      |
| `created_at`            | `TIMESTAMP WITH TIME ZONE` | Timestamp of when the record was created in this database.                  | `ETL Process`                                                                | `NOT NULL, DEFAULT CURRENT_TIMESTAMP`           | `NO`        | `N/A`         | `'2024-05-10 10:00:00+00'`                | `Automatically set on record insertion.`                                                                                            |
| `updated_at`            | `TIMESTAMP WITH TIME ZONE` | Timestamp of when the record was last updated in this database.             | `ETL Process`                                                                | `NOT NULL, DEFAULT CURRENT_TIMESTAMP`           | `NO`        | `N/A`         | `'2024-05-10 10:00:00+00'`                | `Automatically updated on record modification (e.g., via a trigger or by ETL script).`                                              |
| ...                     | ...                    | ...                                                                         | ...                                                                          | ...                                             | ...         | ...           | ...                                       | ...                                                                                                                                 |

---
```

**Explanation of Data Dictionary Fields:**

* **Table Name:** The SQL name of the table (e.g., `PlayerGameStats`).
* **Table Description:** A brief summary of the table's purpose.
* **Column Name:** The SQL name of the column (e.g., `player_id`, `points_scored`).
* **Data Type (PostgreSQL):** The specific PostgreSQL data type (e.g., `INTEGER`, `VARCHAR(255)`, `DATE`, `TIMESTAMP WITH TIME ZONE`, `BOOLEAN`, `DECIMAL(5,2)`, `JSONB`).
* **Description:** A clear, human-readable explanation of what the data in this column represents.
* **Source(s):** The primary origin of the data for this column. List the ETL module, specific API endpoint, Kaggle file, or website section. This helps with data lineage. (e.g., "nba_api: `playergamelog` endpoint, `PTS` field", "Basketball-Reference: Player Season Totals page", "Module 4: Spotrac Scraper").
* **Constraints:** Any database constraints applied to this column (e.g., `PK` (Primary Key), `FK -> referenced_table(referenced_column)` (Foreign Key), `UNIQUE`, `NOT NULL`, `CHECK (condition)`).
* **Nullability:** `YES` if the column can contain `NULL` values, `NO` otherwise.
* **Units:** If the data represents a measurement, specify the units (e.g., "minutes", "points", "percentage", "inches", "USD"). Use "N/A" if not applicable.
* **Examples:** One or two illustrative example values. For dates, show the format (e.g., `'2023-10-24'`).
* **Notes:** Any additional context, historical considerations, calculation methods if the column is derived, how missing data is handled specifically for this column, or any known issues/caveats.

---

### 2. Code & Script Documentation Standards

**A. Python Docstring Format (Google Style Recommended):**

Google style is readable and well-supported by documentation generators like Sphinx.

```python
# Example in a Python script (e.g., data_collection/nba_api_client.py)

def fetch_player_game_logs(player_id: int, season: str, season_type: str = "Regular Season") -> pd.DataFrame | None:
    """Fetches all game logs for a specific player and season from NBA.com.

    Args:
        player_id (int): The official NBA.com player ID.
        season (str): The season identifier, e.g., "2022-23".
        season_type (str, optional): Type of season ("Regular Season", "Playoffs"). 
                                     Defaults to "Regular Season".

    Returns:
        pd.DataFrame | None: A pandas DataFrame containing the player's game logs 
                              for the specified season and type, with columns mapped
                              to the database schema. Returns None if an error occurs
                              or no data is found.
    
    Raises:
        requests.exceptions.RequestException: If a network error occurs during API call.
        ValueError: If season format is invalid.

    Notes:
        - Implements a delay between API calls as configured.
        - Handles API errors and retries.
        - Logs activity and errors.
    """
    # ... function code ...
    pass
```

**B. Header for each Python Script:**

Place this at the top of each `.py` file.

```python
# /nba_historical_data_project/data_collection/nba_api_client.py
"""
Purpose:
    This module is responsible for fetching various data points related to players,
    teams, games, and seasons directly from the official NBA.com API
    (stats.nba.com) using the 'nba_api' Python library.

Author: [Your Name/Username]
Date Created: YYYY-MM-DD
Last Modified: YYYY-MM-DD
Version: 1.0.0

Key Dependencies:
    - nba_api
    - pandas
    - requests (indirectly via nba_api)
    - logging
    - time
    - configparser (for reading config.ini)
    - database.db_manager (for DB interactions)

Main Functions:
    - fetch_all_players_static(): Retrieves static list of all players.
    - fetch_player_career_stats(player_id): Fetches career stats for a player.
    - run_nba_api_collection(): Orchestrates the data collection from NBA API.
"""

# ... imports and rest of the script ...
```

**C. Inline Comments:**

Use inline comments (`#`) to explain complex logic, non-obvious decisions, or specific data transformations.

```python
# Example of an inline comment
# Calculate possession estimate using a simplified formula for older seasons
# where full play-by-play might be missing.
if season_year < 1996:
    estimated_possessions = (fga_team + (0.44 * fta_team) - oreb_team + tov_team)
else:
    # Use more precise formula or actual possession data if available
    estimated_possessions = calculate_possessions_from_pbp(game_id) 
```

---

### 3. README.md Structure

The main `README.md` file in the project root should follow this structure:

```markdown
# Historical NBA Data Collection and Storage Project

## 1. Overview
[Briefly re-state the Project Core Idea: To create a local, queryable PostgreSQL database populated with a highly granular set of historical NBA, BAA, NBL, and ABA data (1946-2025), utilizing exclusively free and open-source software and tools.]

This project aims to provide a comprehensive, locally accessible dataset for personal data analysis, historical research, and basketball-related projects.

## 2. Technology Stack
*   **Language:** Python 3.9+
*   **Data Collection Libraries:** `nba_api`, `basketball_reference_web_scraper`, `requests`, `BeautifulSoup4`, `Scrapy` (selectively)
*   **Data Processing:** `pandas`, `NumPy`
*   **Database:** PostgreSQL (Version X.Y)
*   **Python-DB Connector:** `psycopg2-binary`
*   **Database Management/Querying:** DBeaver Community Edition (or `psql`)
*   **Version Control:** Git, GitHub/GitLab (for personal backup/versioning)
*   **Configuration:** `config.ini`, `.env` (for sensitive credentials)

## 3. Project Setup Instructions

### 3.1. Prerequisites
*   Python 3.9 or higher installed.
*   Git installed.
*   PostgreSQL server installed and running.

### 3.2. Environment Setup
1.  **Clone the repository (if applicable):**
    ```bash
    git clone [repository_url]
    cd nba_historical_data_project
    ```
2.  **Create and activate a Python virtual environment:**
    ```bash
    python -m venv venv
    # On Windows
    .\venv\Scripts\activate
    # On macOS/Linux
    source venv/bin/activate
    ```
3.  **Install Python dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
4.  **Set up PostgreSQL:**
    *   Ensure your PostgreSQL server is running.
    *   Create the database: `CREATE DATABASE nba_data_db;` (using `psql` or DBeaver).
    *   Create a dedicated user and grant privileges:
        ```sql
        CREATE USER nba_user WITH PASSWORD 'your_secure_password';
        GRANT ALL PRIVILEGES ON DATABASE nba_data_db TO nba_user;
        -- If using schemas within the DB, grant usage on schema too.
        ```
5.  **Configure the application:**
    *   Copy `.env.example` to `.env` (if provided for credentials) and fill in your database details:
      ```ini
      # .env
      DB_HOST=localhost
      DB_PORT=5432
      DB_NAME=nba_data_db
      DB_USER=nba_user
      DB_PASSWORD=your_secure_password
      ```
    *   Review `config.ini` for any path settings or parameters you might want to adjust (e.g., API call delays, season ranges for specific runs). Default values are provided.

### 3.3. Initialize Database Schema
Run the main ETL script with a specific flag to set up the schema (or this might be part of the initial run):
```bash
python main_etl.py --setup-schema-only 
```

*(Alternatively, document manual execution of DDL scripts via DBeaver/psql from the `database/schema_ddl/` directory in the correct order.)*

## 4. Directory Structure Overview

[Briefly explain the purpose of key directories like `data_collection/`, `data_processing/`, `database/`, `docs/`, `logs/`, `main_etl.py` based on the file structure defined in Phase 2.]

## 5. How to Run the ETL Pipeline

### 5.1. Initial Full Data Load

To perform a complete historical data backfill (Kaggle, APIs, scrapers for all defined seasons):

```bash
python main_etl.py --run-mode full_historical_load
```

*(This will take a significant amount of time. Monitor logs in `logs/etl.log`.)*

### 5.2. Daily/Regular Updates (for Current Season)

To fetch updates for the current season (e.g., new games, updated stats):

```bash
python main_etl.py --run-mode daily_update --season YYYY 
# Example: python main_etl.py --run-mode daily_update --season 2024 
# (where 2024 is the start year of the 2024-25 season)
```

### 5.3. Specific Module Runs (for Development/Debugging)

(Optional: if you implement such flags in `main_etl.py`)

```bash
python main_etl.py --module kaggle_ingest
python main_etl.py --module nba_api --season 2022
```

## 6. Data Dictionary

The detailed schema for all database tables and columns is available in:
[docs/data_dictionary.md](docs/data_dictionary.md)

## 7. Query Examples

Example SQL queries to explore the database can be found in:
[docs/query_examples.sql](docs/query_examples.sql)

## 8. ETL Process Overview

A high-level description of the ETL data flow is available in:
[docs/etl_process.md](docs/etl_process.md)

## 9. Historical Nuances

Notes on important historical rule changes and statistical definitions:
[docs/historical_nuances.md](docs/historical_nuances.md)

## 10. Troubleshooting / FAQ

* **Problem:** Scraper for Basketball-Reference.com stops working.
  * **Solution:** Basketball-Reference.com might have changed its HTML structure. The selectors in `data_collection/custom_scrapers/bball_ref_custom_scraper.py` or the `basketball_reference_web_scraper` library itself may need updating. Check the library's GitHub page for issues/updates.
* **Problem:** `nba_api` calls are failing.
  * **Solution:** Check your internet connection. The NBA API might be temporarily down or you might be getting rate-limited (ensure delays in `config.ini` are respected). Check the `nba_api` library's GitHub page.
* **Problem:** Slow query performance.
  * **Solution:** Ensure all relevant columns used in `WHERE` clauses or `JOIN`s are indexed. Analyze the query plan using `EXPLAIN ANALYZE your_query;` in PostgreSQL.

## 11. Data Sources and Acknowledgements

This project utilizes data from the following primary sources:

* **NBA.com API (stats.nba.com):** Accessed via the `nba_api` Python library.
* **Basketball-Reference.com:** Accessed via the `basketball_reference_web_scraper` Python library and custom scrapers.
* **Kaggle Datasets:**
  * "Complete NBA Database and Historical Box Scores" by Eoin A Moore.
  * "NBA Database" by Wyatt O'Walsh.
* **Spotrac.com:** Accessed via custom scrapers for contract details.

This project is for personal, non-commercial use only and aims to adhere to the Terms of Service of all data providers.

---

```

---

### 4. SQL Query Style Guide (for `docs/query_examples.sql` and ad-hoc queries)

1.  **Keyword Casing:** Use `UPPERCASE` for all SQL keywords (`SELECT`, `FROM`, `WHERE`, `JOIN`, `GROUP BY`, `ORDER BY`, `AND`, `OR`, `AS`, `ON`, `CASE`, `WHEN`, `THEN`, `END`, etc.).
    ```sql
    SELECT player_name FROM Players WHERE birth_city = 'Akron';
    ```
2.  **Identifiers Casing:** Use `snake_case` (lowercase with underscores) for table names and column names, as defined in your schema. If you must quote an identifier (e.g., it contains spaces or is a reserved word, though this should be avoided in schema design), use double quotes.
    ```sql
    SELECT p.first_name, p.last_name, ps.points
    FROM players AS p
    JOIN player_season_totals AS ps ON p.player_id = ps.player_id;
    ```
3.  **Indentation and Line Breaks:**
    *   Indent clauses for readability.
    *   Start new clauses (`FROM`, `WHERE`, `JOIN`, `GROUP BY`, `ORDER BY`) on new lines.
    *   Break up long lists of selected columns or conditions onto multiple lines, indented.
    ```sql
    SELECT
        p.player_id,
        p.first_name,
        p.last_name,
        s.season_year,
        pst.games_played,
        pst.points_total,
        pst.assists_total
    FROM
        players AS p
    JOIN
        player_season_totals AS pst ON p.player_id = pst.player_id
    JOIN
        seasons AS s ON pst.season_id = s.season_id
    WHERE
        p.last_name = 'James'
        AND s.season_year >= '2019-20'
        AND pst.season_type = 'Regular Season'
    ORDER BY
        s.season_year DESC,
        pst.points_total DESC;
    ```
4.  **Comments:**
    *   Use `--` for single-line comments.
    *   Use `/* ... */` for multi-line comments.
    ```sql
    -- Select all games for a specific player
    SELECT
        g.game_date,
        ht.team_name AS home_team,
        at.team_name AS away_team,
        pgs.points_scored
    FROM
        player_game_stats AS pgs
    JOIN
        games AS g ON pgs.game_id = g.game_id
    JOIN
        players AS p ON pgs.player_id = p.player_id
    JOIN
        teams AS ht ON g.home_team_id = ht.team_id -- Home team
    JOIN
        teams AS at ON g.away_team_id = at.team_id -- Away team
    /*
    Filter for LeBron James' games
    where he scored 30 or more points.
    */
    WHERE
        p.first_name = 'LeBron' AND p.last_name = 'James'
        AND pgs.points_scored >= 30
    ORDER BY
        g.game_date DESC;
    ```
5.  **Aliasing:**
    *   Use concise and meaningful aliases for tables, especially in joins (e.g., `p` for `players`, `g` for `games`).
    *   Use the `AS` keyword for table and column aliases for clarity.
    ```sql
    SELECT
        p.last_name || ', ' || p.first_name AS full_name,
        t.team_name AS current_team
    FROM
        players AS p
    JOIN
        teams AS t ON p.current_team_id = t.team_id; -- Assuming such a column exists
    ```
6.  **Use of Parentheses:** Use parentheses to clarify the order of operations in complex `WHERE` clauses, even if not strictly necessary, for readability.
    ```sql
    WHERE
        (g.season_type = 'Regular Season' AND g.game_date > '2020-01-01')
        OR (g.season_type = 'Playoffs' AND g.round_number > 1)
    ```
7.  **Avoid `SELECT *` in production scripts/final queries:** Explicitly list the columns you need. This makes queries more readable, maintainable, and less prone to breaking if table structures change. (`SELECT *` is fine for ad-hoc exploration in DBeaver).

---

### 5. Naming Conventions (Summary)

This summarizes conventions established throughout your plan:

*   **PostgreSQL Tables:** `snake_case`, plural (e.g., `player_game_stats`, `teams`, `seasons`).
*   **PostgreSQL Columns:** `snake_case` (e.g., `player_id`, `points_scored`, `game_date`, `season_year`).
*   **PostgreSQL Schemas (if used):** `snake_case` (e.g., `raw_data`, `production`). (Not explicitly planned yet, but good to note).
*   **PostgreSQL Indexes:** `idx_tablename_columnnames` (e.g., `idx_player_game_stats_player_id_game_id`) or `ix_tablename_columnnames`.
*   **PostgreSQL Foreign Key Constraints:** `fk_childtable_parenttable_column` (e.g., `fk_player_game_stats_players_player_id`).
*   **Python Scripts (.py files):** `snake_case.py` (e.g., `nba_api_client.py`, `main_etl.py`).
*   **Python Modules/Packages (directories):** `snake_case` (e.g., `data_collection`, `custom_scrapers`).
*   **Python Variables & Functions:** `snake_case` (e.g., `player_id_list`, `fetch_data_from_api()`).
*   **Python Classes:** `PascalCase` (e.g., `NbaApiClient`, `DataCleaner`).
*   **Python Constants:** `UPPER_SNAKE_CASE` (e.g., `DEFAULT_API_DELAY = 3`).
*   **Configuration File Keys (`config.ini`):** `snake_case` (e.g., `db_host`, `api_delay_seconds`).

---

