# NBA Historical Database & ETL Pipeline

## 1. Project Goal

This project aims to create a comprehensive, locally queryable PostgreSQL database populated with a highly granular set of historical NBA (and related leagues like BAA, NBL, ABA) data, spanning from 1946 to 2025. The data will be collected, cleaned, and loaded via a Python-based ETL pipeline, utilizing exclusively free and open-source software and tools.

The primary goal is to provide a rich dataset for personal data analysis, historical research, and basketball-related projects.

## 2. Current Project Status

**Phase:** Initial Schema Implementation Complete; Transitioning to ETL Development.

**Key Milestones Achieved:**
*   **Detailed Planning:** Comprehensive project specification, design documents, and build plans are complete and available in the `planning/` directory.
*   **Environment Setup:**
    *   Python (3.10.11) and Git (2.49.0) are installed and verified.
    *   A Python virtual environment (`venv`) is set up and activated, with all dependencies from `requirements.txt` installed.
    *   PostgreSQL server (Version 17) is installed, and the `nba_data_db` database has been created along with a dedicated user (`user`).
    *   DBeaver is connected to the database for management.
    *   `config.ini` and `.env` files are structured for project configuration and sensitive credentials.
*   **Database Schema Implementation:**
    *   Core schema tables (leagues, seasons, franchises, teams) have been created and verified.
    *   DDL scripts (001_create_leagues_seasons.sql, 002_create_franchises_teams.sql) have been successfully executed.
    *   Database management utilities (`database/db_manager.py`) implemented with support for DDL execution and DataFrame loading.
    *   Initial database setup script (`db/nba_data_db.sql`) for user roles and permissions is in place.
*   **Version Control:** The project is initialized as a Git repository, with initial project files, Memory Bank, and planning documents committed.
*   **Memory Bank & Context Management:**
    *   Cline's Memory Bank (`memory-bank/`) is initialized to maintain project context.
    *   ConPort database (`context_portal/`) set up for structured project context management.
*   **MCP Integration:**
    *   The `@modelcontextprotocol/server-postgres` MCP server is configured and successfully tested, providing read-only SQL access to `nba_data_db`.
    *   Research into other potentially useful MCP servers has been conducted and documented in `planning/nbadb/mcplist.md`.

**Next Immediate Steps:**
1.  Develop ETL orchestration script to coordinate data extraction, transformation, and loading processes.
2.  Implement data extraction components for planned data sources (Kaggle datasets, nba-api, basketball-reference-web-scraper).
3.  Analyze and prioritize additional MCP servers for installation to support ETL development.
4.  Continue database schema implementation with additional tables as outlined in planning documents.

## 3. Planned Features (MVP)

*   **Comprehensive Data Collection:** Ingest data from Kaggle, NBA.com API, Basketball-Reference.com, and Spotrac.com.
*   **Historical Coverage:** Data spanning NBA, BAA, NBL, ABA leagues from 1946-2025.
*   **Granular Data Points:** Player info, team info, game stats, season stats, awards, draft details, transactions, contract information.
*   **Robust ETL Pipeline:** Python-based pipeline for data extraction, cleaning, transformation, conflict resolution, and loading.
*   **Queryable PostgreSQL Database:** Well-structured relational database.
*   **Foundational Documentation:** Data dictionary, ETL process overview, historical nuances.

## 4. Technology Stack

*   **Programming Language:** Python 3.9+ (currently 3.10.11)
*   **Database:** PostgreSQL (Version 17)
*   **Core Python Libraries:**
    *   `pandas`, `numpy` (Data manipulation)
    *   `requests`, `BeautifulSoup4` (Web scraping & API interaction)
    *   `Scrapy` (Web scraping, planned)
    *   `nba_api`, `basketball_reference_web_scraper` (Specific data source clients, planned)
    *   `psycopg2-binary` (PostgreSQL connector)
    *   `python-dotenv` (Environment variable management)
*   **Version Control:** Git
*   **Context Management:** ConPort with MCP integration
*   **MCP Servers:** `@modelcontextprotocol/server-postgres` (active), others under consideration.

## 5. Project Structure Overview

**Current Directories:**
*   `.git/`: Git repository.
*   `.vscode/`: VS Code configuration.
*   `context_portal/`: Contains ConPort database for project context management.
*   `data/`: Contains data directories including `sqlite_databases/`.
*   `database/`: Contains Python modules for database management (`db_manager.py`) and DDL scripts (`schema_ddl/`).
*   `db/`: Contains database-related files, including initial DDL (`nba_data_db.sql`).
*   `memory-bank/`: Cline's Memory Bank for project context and progress tracking.
*   `planning/`: Contains all initial planning documents, specifications, and designs (includes `global_plans/` and `nbadb/`).
*   `venv/`: Python virtual environment.

**Current Files:**
*   `config.ini`: Main configuration file with settings for data sources, APIs, and database.
*   `.env`: For sensitive credentials (e.g., database password). **Ensure this is in `.gitignore` and created locally.**
*   `requirements.txt`: Python dependencies.
*   `README.md`: This file.

**Planned Future Components:**
*   `data_collection/`: (To be created) Modules for fetching data from various sources.
*   `data_processing/`: (To be created) Modules for cleaning, transforming, and standardizing data.
*   `docs/`: (To be created) Will contain project documentation (Data Dictionary, ETL Process, etc.).
*   `logs/`: (To be created) For ETL log files.
*   `tests/`: (To be created) For unit and integration tests.
*   `main_etl.py`: (To be created) Main script to orchestrate the ETL pipeline.

## 6. Setup Instructions (Summary)

1.  **Prerequisites:**
    *   Python 3.9+
    *   Git
    *   PostgreSQL Server (Version 17 recommended)
2.  **Clone Repository:** `git clone <repository-url>`
3.  **Navigate to Project Directory:** `cd Database` (or the cloned directory name)
4.  **Create & Activate Virtual Environment:**
    *   `python -m venv venv`
    *   Windows: `venv\\Scripts\\activate`
    *   macOS/Linux: `source venv/bin/activate`
5.  **Install Dependencies:** `pip install -r requirements.txt`
6.  **PostgreSQL Setup:**
    *   Ensure PostgreSQL server is running.
    *   Create a database (e.g., `nba_data_db`).
    *   Create a user (e.g., `user`) with a password and grant necessary privileges to the database and public schema (see `db/nba_data_db.sql` for initial role/privilege setup).
7.  **Configure Application:**
    *   Copy `config.ini.example` to `config.ini` (if an example is provided) and update paths/settings as needed. (Currently, `config.ini` is directly in the repo).
    *   Create a `.env` file in the project root (it's gitignored). Add your database credentials:
        ```env
        DB_USER=your_db_user
        DB_PASSWORD=your_db_password
        DB_HOST=localhost
        DB_PORT=5432
        DB_NAME=nba_data_db
        ```
8.  **Database Schema:** The initial schema DDL scripts are located in `database/schema_ddl/` and can be executed via `database/db_manager.py`. Core schema tables (leagues, seasons, franchises, teams) have already been implemented.

## 7. How to Run (Planned)

The ETL pipeline will be orchestrated by `main_etl.py`.
```bash
python main_etl.py [arguments/flags for specific operations]
```
Detailed run instructions will be added once the script is developed.

## 8. Development Notes

*   This project utilizes Cline, an AI software engineer. Project context and ongoing progress are maintained in the `memory-bank/` directory.
*   Additional project context is managed through ConPort in the `context_portal/` directory.
*   Refer to `planning/` for detailed design documents and project specifications.

---

This README provides an overview of the NBA Historical Database project. It will be updated as development progresses.
