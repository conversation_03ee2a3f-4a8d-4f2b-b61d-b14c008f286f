# System Patterns

## System Architecture
*   **Layered ETL Architecture:** The system is designed as a multi-layered ETL (Extract, Transform, Load) pipeline.
    *   **Extraction Layer:** Modules dedicated to fetching raw data from various sources (primarily within `etl/extractors.py`). Sources include Kaggle datasets, NBA.com API, and Basketball-Reference.com.
    *   **Transformation Layer:** Modules for cleaning, transforming, standardizing, and resolving conflicts in the collected data (primarily within `etl/transformers.py` and specific transformation logic within `etl/etl_orchestrator.py`).
    *   **Loading Layer:** Modules responsible for loading processed data into the database (primarily within `etl/loaders.py` and `database/db_manager.py`).
    *   **Data Storage Layer:** A PostgreSQL database (`database/`) with a well-defined schema (defined in `database/schema_ddl/`) to store the processed data.
    *   **Orchestration Layer:** The `etl/etl_orchestrator.py` script manages the execution flow of the entire pipeline, coordinating extractors, transformers, and loaders. `etl/run_etl.py` serves as the command-line entry point.
*   **Modular Design:** Each data source and major processing step is encapsulated in its own Python module or class, promoting separation of concerns and maintainability (e.g., `WyattKaggleCSVExtractor` in `extractors.py`, `WyattFranchiseTeamTransformer` in `transformers.py`).
*   **Configuration Driven:** Key parameters like file paths, API delays, and database credentials are managed through external configuration files (`config.ini`, `.env`).

## Key Technical Decisions
*   **Database Choice:** PostgreSQL was chosen for its robustness, SQL compliance, and features suitable for relational data.
*   **Primary Language:** Python 3.9+ is used for the entire ETL pipeline due to its strong data manipulation libraries (Pandas, NumPy) and ecosystem for web scraping and API interaction.
*   **Data Ingestion Strategy:** A hybrid approach combining bulk loading from local datasets (Kaggle), systematic API consumption (`nba_api`, `basketball_reference_web_scraper`). Custom web scraping is planned.
*   **Idempotency:** ETL processes, especially data loading, aim for idempotency where appropriate (e.g., using ON CONFLICT clauses or truncating tables before full reloads for certain datasets).
*   **Centralized Logging:** Implementation of comprehensive logging using Python's `logging` module to track ETL operations, errors, and data flow, with logs stored in the `logs/` directory.
*   **Source Precedence:** A defined hierarchy for data conflict resolution (NBA.com API > Basketball-Reference.com > Kaggle > Custom Scrapers) is a design goal, though current implementation focuses on distinct data streams.

## Design Patterns in Use
*   **Modular Programming:** The system is broken down into smaller, independent, and interchangeable modules/classes (e.g., `DatabaseManager`, `ETLOrchestrator`, various Extractor and Loader classes).
*   **Layered Architecture:** As described in System Architecture.
*   **Configuration Management:** Externalizing configuration details from code.
*   **Orchestrator/Controller Pattern:** `etl/etl_orchestrator.py` acts as a central controller for the ETL workflow.
*   **Data Access Object (DAO) like pattern:** `database/db_manager.py` encapsulates database interaction logic.
*   **Strategy Pattern (Implicit):** Different extractor and transformer classes can be seen as strategies for handling different data sources/types.

## Component Relationships
*   **`etl/run_etl.py` (Entry Point):**
    *   Parses command-line arguments.
    *   Sets up logging.
    *   Instantiates and runs `ETLOrchestrator`.
*   **`etl/etl_orchestrator.py` (Orchestrator):**
    *   Reads configuration from `config.ini` and uses `DatabaseManager` which handles `.env`.
    *   Initializes database schema via `DatabaseManager` and DDL scripts.
    *   Coordinates the ETL process for specified sources and tables.
    *   Calls appropriate methods in `etl/extractors.py` (via specific extractor classes like `WyattKaggleCSVExtractor`) to fetch raw data.
    *   Calls transformation logic (e.g., `_transform_eoin_moore_data` method, or methods in `etl/transformers.py` like `WyattFranchiseTeamTransformer`).
    *   Uses `etl/loaders.py` (e.g., `PostgreSQLLoader`) and `database/db_manager.py` to load processed data into PostgreSQL.
    *   Writes logs to `logs/etl_TIMESTAMP.log`.
*   **`etl/extractors.py` Modules (e.g., `WyattKaggleCSVExtractor`, `NBAAPIExtractor`):**
    *   Interact with external APIs or read local files (CSVs).
    *   Produce raw or semi-structured data (often pandas DataFrames).
*   **`etl/transformers.py` Modules (e.g., `WyattFranchiseTeamTransformer`):**
    *   Receive DataFrames from orchestrator/extractors.
    *   Perform cleaning, transformation, standardization, and conflict resolution.
    *   May read mapping tables or canonical lists from the database via `DatabaseManager`.
    *   Output processed DataFrames ready for final loading.
*   **`etl/loaders.py` Modules (e.g., `PostgreSQLLoader`):**
    *   Receive processed DataFrames.
    *   Utilize `DatabaseManager` to load data into PostgreSQL tables.
*   **`database/db_manager.py`:**
    *   Handles DB connections (using credentials from `.env`).
    *   Executes DDL scripts from `database/schema_ddl/`.
    *   Provides methods for executing queries and loading pandas DataFrames into tables (e.g., `load_df_to_table`).
*   **`database/schema_ddl/`:** Contains SQL scripts defining the database structure.
*   **`etl/schema_utils.py`:** Provides utilities for inspecting and managing database schema information.
*   **`docs/`:** Contains documentation, including `data_dictionary.md` which is critical for understanding the database schema used by all components.

## Critical Implementation Paths
1.  **Database Schema Setup:** The successful execution of DDL scripts in `database/schema_ddl/` via `db_manager.py` is foundational.
2.  **Core Database Utilities (`database/db_manager.py`):** Implementation of reliable database connection and data loading functions (especially `load_df_to_table`) is critical.
3.  **Configuration Loading (`config.ini`, `.env`):** Correctly loading configuration is essential for all modules. `etl_orchestrator.py` handles `config.ini`, and `db_manager.py` handles `.env`.
4.  **ETL Orchestration (`etl/etl_orchestrator.py`):** The main script must correctly sequence calls to extraction, transformation, and loading components.
5.  **Data Extraction Logic (`etl/extractors.py`):** Reliable extraction from various sources.
6.  **Data Transformation Logic (`etl/transformers.py`, methods in `etl_orchestrator.py`):** Accurate cleaning, mapping, and structuring of data.
7.  **Data Loading Logic (`etl/loaders.py`, `database/db_manager.py`):** Ensuring data can be inserted or updated correctly (e.g., handling `if_exists` strategies, unique constraints).
8.  **Error Handling and Logging:** Implementing robust error handling and comprehensive logging across all modules is vital for troubleshooting and monitoring.
