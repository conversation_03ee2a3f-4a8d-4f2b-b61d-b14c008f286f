## Overall File System Structure

This structure aims to organize the project logically, separating concerns and making it easier to navigate and maintain.

```
/nba_historical_data_project/
├── .env                      # For environment variables (DB credentials, API keys - if any in future) - Add to .gitignore
├── .gitignore                # Specifies intentionally untracked files that G<PERSON> should ignore
├── config.ini                # Configuration file for non-sensitive parameters (paths, delays, season settings)
├── main_etl.py               # Master script to orchestrate the entire ETL pipeline
├── requirements.txt          # List of Python dependencies for pip
├── README.md                 # Main project documentation
│
├── data_collection/          # Modules for fetching raw data from various sources
│   ├── __init__.py
│   ├── kaggle_ingestor.py    # Module 1: Kaggle dataset ingestion
│   ├── nba_api_client.py     # Module 2: NBA.com API client
│   ├── bball_ref_client.py   # Module 3: Basketball-Reference.com client (using library)
│   ├── custom_scrapers/      # Sub-package for Module 4: Custom Web Scraping
│   │   ├── __init__.py
│   │   ├── bball_ref_custom_scraper.py
│   │   ├── spotrac_scraper.py
│   │   └── utils_scraping.py # Common scraping utilities (e.g., polite request functions)
│   └── collection_utils.py   # Common utilities for data collection modules
│
├── data_processing/          # Modules for cleaning, transforming, and standardizing data
│   ├── __init__.py
│   ├── cleaner.py            # Core data cleaning functions
│   ├── transformer.py        # Core data transformation functions (schema mapping, etc.)
│   ├── name_standardizer.py  # Player/Team name standardization logic
│   ├── conflict_resolver.py  # Logic for resolving data conflicts based on precedence
│   └── processing_utils.py   # Common utilities for data processing
│
├── database/                 # Database related scripts and utilities
│   ├── __init__.py
│   ├── schema_ddl/           # Directory for SQL DDL scripts
│   │   ├── 001_create_leagues_seasons.sql
│   │   ├── 002_create_franchises_teams.sql
│   │   ├── 003_create_players_personnel.sql
│   │   ├── 004_create_games_stats.sql
│   │   ├── 005_create_awards_draft_transactions.sql
│   │   ├── 006_create_contracts_salaries.sql
│   │   ├── 007_create_referees_hof_misc.sql
│   │   └── 008_create_indexes.sql
│   │   └── 009_create_mapping_review_tables.sql # For player_identifier_mapping, unmatched_player_names_review etc.
│   ├── db_manager.py         # Handles DB connection, DDL execution, data loading
│   └── db_utils.py           # Lower-level DB utility functions (e.g., bulk insert helpers)
│
├── logs/                     # Directory for log files (ensure this is in .gitignore if logs are verbose)
│   └── etl.log
│
├── docs/                     # Project documentation
│   ├── data_dictionary.md    # Detailed schema documentation
│   ├── etl_process.md        # Overview of the ETL pipeline flow and module interactions
│   ├── query_examples.sql    # Example SQL queries for common use cases
│   └── historical_nuances.md # Notes on rule changes, stat definition changes
│
├── tests/                    # Automated tests
│   ├── __init__.py
│   ├── test_data_collection/
│   │   ├── __init__.py
│   │   └── test_nba_api_client.py # Example test file
│   ├── test_data_processing/
│   │   ├── __init__.py
│   │   └── test_name_standardizer.py # Example test file
│   └── test_utils/
│       └── sample_data/      # Sample raw data files for testing
│
└── venv/                     # Python virtual environment (typically in .gitignore)
```

---

## Detailed Technical Specifications for MVP Features

Here are the specifications for each MVP feature.

### 1. Feature Name: Database Setup & Schema Implementation

* **Intent/Goal of Feature:** To create the PostgreSQL database structure (tables, columns, relationships, constraints, indexes) that will store all collected historical NBA data, ensuring data integrity and queryability.
* **APIs Involved (Internal/External):**
  * Internal: `psycopg2` (via `db_manager.py`) to connect to PostgreSQL and execute DDL.
  * External: PostgreSQL server.
* **Detailed Technical Requirements/User Stories:**
  * The PostgreSQL database (e.g., `nba_data_db`) must be created.
  * A dedicated PostgreSQL user with full privileges on this database must be created and used for all operations.
  * All SQL DDL scripts located in `database/schema_ddl/` must be executable in a defined order to create the entire schema.
  * DDL scripts must define all tables, columns with correct data types (as per plan, e.g., `INTEGER`, `VARCHAR`, `DATE`, `TIMESTAMP WITH TIME ZONE`, `DECIMAL`, `BOOLEAN`, `JSONB`).
  * Primary Keys (PK) must be defined for all tables.
  * Foreign Keys (FK) must be defined to enforce relationships between tables, with appropriate `ON DELETE` / `ON UPDATE` actions (e.g., `CASCADE`, `SET NULL`, `RESTRICT`).
  * `UNIQUE` constraints must be applied where necessary (e.g., unique player identifiers from a source).
  * `NOT NULL` constraints must be applied to mandatory columns.
  * `CHECK` constraints can be used for simple data validation rules at the DB level (e.g., stat value > 0).
  * Indexes must be created on all PKs, FKs, and columns frequently used in `WHERE` clauses, `JOIN` conditions, or `ORDER BY` clauses to optimize query performance.
  * Auxiliary tables like `player_identifier_mapping`, `team_identifier_mapping`, and `unmatched_player_names_review` must be created.
  * The `db_manager.py` module should have a function to execute all DDL scripts in the correct sequence.
* **Overall System Architecture Context:** This is the foundational storage layer. All data collection and processing modules will ultimately write to or read from the schema defined here. The `main_etl.py` script will likely call a function in `db_manager.py` to ensure the schema exists or to set it up initially.
* **Database Schema Impact:** This feature *defines* the entire database schema. All tables listed in your comprehensive plan (Leagues, Seasons, Franchises, Teams, Players, Games, PlayerGameStats, etc.) will be created.
* **API Endpoint Specifications (Internal Script Structure - `database/db_manager.py`):**
  * `Key function definitions`:
    * `connect_db(config)`: Establishes and returns a PostgreSQL connection object.
    * `execute_sql_script(connection, script_path)`: Executes a single SQL DDL file.
    * `setup_schema(config, ddl_scripts_path)`: Iterates through DDL files in order and executes them.
    * `check_schema_exists(config)`: (Optional) A function to verify if key tables exist.
  * `Input parameters`: Connection details (from `config.ini`), paths to DDL scripts.
  * `Expected output/return values`: Success/failure status, connection objects.
  * `Core logic`: Read SQL from files, execute using `psycopg2` cursors, commit transactions.
  * `Error handling`: Try-except blocks for `psycopg2` errors (connection errors, SQL syntax errors), log errors, potentially raise exceptions to halt setup if critical.
* **CRUD Operations:** Primarily Create (DDL `CREATE TABLE`, `CREATE INDEX`, etc.).
* **Security Considerations:**
  * Database credentials should be managed securely (e.g., via `.env` file or environment variables, read from `config.ini` which is not committed if sensitive).
  * The dedicated database user should have permissions scoped only to the `nba_data_db` database.
* **Testing Strategy Notes:**
  * Manually verify schema creation using DBeaver or `psql`.
  * Write a simple Python script to connect and list tables to confirm creation.
  * Test FK constraints by attempting to insert invalid data.
  * Ensure DDL scripts are idempotent where possible (e.g., using `CREATE TABLE IF NOT EXISTS`), or can be run on an empty database.
* **Data Management & Error Logging Notes:**
  * Log success or failure of each DDL script execution.
  * Version control DDL scripts in Git.
  * Document the order of DDL script execution if there are dependencies.

---

### 2. Feature Name: Kaggle Dataset Ingestion (Module 1)

* **Feature Name:** Module 1: Kaggle Dataset Ingestion
* **Intent/Goal of Feature:** To perform an initial bulk load of foundational historical NBA data from the specified Kaggle datasets ("Complete NBA Database and Historical Box Scores" by Eoin A Moore, "NBA Database" by Wyatt O'Walsh) into the PostgreSQL database.
* **APIs Involved (Internal/External):**
  * Internal: `pandas` (to read CSVs), `sqlite3` (to read SQLite), `psycopg2` (via `db_manager.py` or `db_utils.py` for writing to PostgreSQL).
  * External: Local file system (accessing downloaded Kaggle files).
* **Detailed Technical Requirements/User Stories:**
  * Script must be able to locate and read specified CSV files (e.g., from Eoin A Moore's dataset).
  * Script must be able to connect to and query the specified SQLite database file (Wyatt O'Walsh's dataset).
  * For each relevant data entity (e.g., games, player stats, team stats, player info, draft history) from Kaggle:
    * Extract data into pandas DataFrames.
    * Map columns from Kaggle datasets to the corresponding columns in the PostgreSQL schema tables.
    * Perform necessary data type conversions (e.g., strings to dates, strings to numbers). Handle potential errors during conversion (e.g., malformed dates).
    * Perform initial basic data cleaning (e.g., trim whitespace, handle common placeholder for missing values like "N/A" by converting to `None` for `NULL` insertion).
    * Load the transformed data into the appropriate PostgreSQL tables. This might involve staging tables first if complex transformations or conflict resolution with other sources is needed immediately, but for MVP, direct load to final tables (if schema matches closely) or simple staging tables is acceptable.
  * Log the number of records processed from each file/Kaggle table and the number of records inserted into each PostgreSQL table.
  * Handle potential file not found errors gracefully.
* **Overall System Architecture Context:** This module is one of the first data collection steps, providing a historical baseline. Its output dataframes/structured data will be loaded into PostgreSQL. It may feed identifiers (like player names) into the `Data Cleaning and Transformation Layer (Module 5)` for standardization.
* **Database Schema Impact:** Populates tables like `Games`, `PlayerGameStats`, `TeamGameStats`, `Players` (basic info), `DraftPicks`, `Teams` (franchise history if available). The exact tables depend on the content of the Kaggle datasets and their mapping to the schema.
* **API Endpoint Specifications (Internal Script Structure - `data_collection/kaggle_ingestor.py`):**
  * `Key function definitions`:
    * `load_eoin_moore_dataset(csv_file_paths, db_conn_params)`
    * `process_games_csv(df, target_table_name)`
    * `process_player_boxscores_csv(df, target_table_name)`
    * `load_wyatt_owalsh_dataset(sqlite_db_path, db_conn_params)`
    * `process_play_by_play_table(sqlite_df, target_table_name)`
    * `map_columns_and_transform(df, column_mapping, type_conversions)`
  * `Input parameters`: File paths to Kaggle datasets (from `config.ini`), database connection parameters.
  * `Expected output/return values`: Pandas DataFrames (intermediate), status messages.
  * `Core logic`:
    * Use `pd.read_csv()` and `sqlite3.connect()` to read data.
    * Iterate through rows/chunks of data.
    * Apply column renaming based on a predefined mapping dictionary.
    * Apply data type conversions (e.g., `pd.to_datetime`, `pd.to_numeric`).
    * Use functions from `db_manager.py` or `db_utils.py` for bulk inserting DataFrames into PostgreSQL (e.g., using `psycopg2.extras.execute_values` or `df.to_sql` with careful consideration of performance and error handling).
  * `Error handling`: Try-except blocks for file I/O errors, data conversion errors, database insertion errors. Log errors with specific details (e.g., problematic file, row, or value).
  * `Rate limiting/politeness considerations`: N/A (local file access).
* **CRUD Operations:** Primarily Create (inserting new records into PostgreSQL tables).
* **Security Considerations:** Ensure file paths in `config.ini` do not allow arbitrary file access if user-configurable (though less of a risk in this personal project).
* **Testing Strategy Notes:**
  * Unit test column mapping and data transformation functions with sample DataFrames.
  * Use small sample CSV/SQLite files for integration testing the full load process into a test database.
  * Verify data counts and spot-check data in PostgreSQL tables post-ingestion.
  * Test handling of missing files or malformed data within the Kaggle datasets.
* **Data Management & Error Logging Notes:**
  * Log start and end of processing for each Kaggle file/table.
  * Log counts of rows read and rows inserted.
  * Report any data transformation issues or records that could not be loaded, along with reasons.
  * Strategy for re-running: Scripts should ideally be runnable multiple times without duplicating data if run against an empty target table, or use staging tables and an upsert logic if re-running to supplement. For MVP, clearing target tables before re-run might be acceptable if it's purely an initial load.

---

### 3. Feature Name: nba_api Client Scripts (Module 2)

* **Feature Name:** Module 2: `nba_api` Client Scripts
* **Intent/Goal of Feature:** To systematically fetch a comprehensive range of current and historical NBA data directly from NBA.com's official API endpoints using the `nba_api` Python library.
* **APIs Involved (Internal/External):**
  * Internal: `nba_api` library functions, `pandas` for data manipulation, `psycopg2` (via `db_manager.py`/`db_utils.py`) for DB interaction.
  * External: NBA.com API endpoints (abstracted by `nba_api` library).
* **Detailed Technical Requirements/User Stories:**
  * Identify all relevant `nba_api` endpoints (e.g., `playercareerstats`, `playergamelog`, `teamgamelogs`, `draftcombineplayeranthro`, `commonallplayers`, `teamdetails`, `franchisehistory`, etc.). A comprehensive list should be compiled based on the project's data scope.
  * Scripts must iterate through necessary identifiers:
    * Player IDs (obtained from `nba_api.stats.static.players.get_players()`).
    * Team IDs (obtained from `nba_api.stats.static.teams.get_teams()`).
    * Season ranges (e.g., 1946-47 to 2024-25, formatted as required by the API).
  * For each endpoint and identifier combination:
    * Make API requests using `nba_api` library functions.
    * Implement a mandatory delay (e.g., 1-3 seconds, configurable in `config.ini`) between API calls to respect NBA.com's servers.
    * Parse the JSON/DataFrame responses provided by `nba_api`.
    * Transform the fetched data: map API response fields to PostgreSQL schema columns, convert data types, handle missing values (`None`).
    * Load new data or update existing records in the appropriate PostgreSQL tables (idempotency is key for re-runs).
  * Implement robust error handling for API request failures (e.g., network issues, rate limiting, invalid parameters, empty responses for valid queries). Include retry mechanisms with backoff for transient errors.
  * Log all API calls, parameters used, number of records fetched, and any errors encountered.
* **Overall System Architecture Context:** This module is a primary data collection component, especially for recent and official NBA data. It feeds raw or semi-processed data into the `Data Cleaning and Transformation Layer (Module 5)` before final loading, or directly if transformations are simple.
* **Database Schema Impact:** Populates a wide range of tables, including `Players` (detailed info), `PlayerSeasonTotals`, `PlayerGameStats`, `TeamSeasonTotals`, `TeamGameStats`, `Games`, `DraftPicks`, `Awards` (if available via API), `Teams`, `Franchises`.
* **API Endpoint Specifications (Internal Script Structure - `data_collection/nba_api_client.py`):**
  * `Key function definitions`:
    * `fetch_all_players_static()`
    * `fetch_all_teams_static()`
    * `fetch_player_career_stats(player_id)`
    * `fetch_player_game_logs(player_id, season)`
    * `fetch_team_game_logs(team_id, season)`
    * `fetch_season_schedule(season)`
    * `process_api_response(response_data, target_table_name, column_mapping)`
    * A main orchestrator function within the module: `run_nba_api_collection(seasons_to_fetch, specific_players=None, specific_teams=None)`
  * `Input parameters`: Season ranges, lists of player/team IDs (can be fetched dynamically or provided), API call delay from `config.ini`.
  * `Expected output/return values`: DataFrames (intermediate), status messages.
  * `Core logic`:
    * Loop through seasons, players, teams.
    * Construct parameters for `nba_api` function calls.
    * Call `nba_api` functions (e.g., `playercareerstats.PlayerCareerStats(player_id=...).get_data_frames()`).
    * Implement `time.sleep(delay)` between calls.
    * Extract relevant data from `nba_api`'s output (often a list of DataFrames).
    * Apply transformations (column mapping, type conversion).
    * Use `db_manager.py` for upserting/inserting data into PostgreSQL.
  * `Error handling`:
    * Try-except blocks around API calls for `requests.exceptions.RequestException`, `nba_api` specific exceptions.
    * Implement a retry loop (e.g., 3 retries with exponential backoff) for common HTTP errors (5xx, timeouts).
    * Log errors with endpoint, parameters, and error details. If an endpoint consistently fails for a player/season, log and skip.
  * `Rate limiting/politeness considerations`: Crucial. Configurable delay (e.g., `nba_api_delay_seconds` in `config.ini`) strictly enforced between all API calls. Default to a conservative value (e.g., 3 seconds).
* **CRUD Operations:** Primarily Create (new data) and Update (e.g., updating player stats for an ongoing season, adding new games). Implement upsert logic (insert or update on conflict).
* **Security Considerations:** N/A for API keys with `nba_api` as it's generally unauthenticated. Focus on polite scraping.
* **Testing Strategy Notes:**
  * Unit test transformation functions with sample API response snippets (mocked).
  * Mock `nba_api` calls to test script logic without hitting the live API (e.g., using `unittest.mock.patch`).
  * Integration test: Run script for a very small subset of data (e.g., one player for one season) and verify data in DB.
  * Test error handling (e.g., simulate API errors, network down).
* **Data Management & Error Logging Notes:**
  * Log each API endpoint called, parameters, and number of records retrieved.
  * Log any transformation warnings or errors.
  * Clearly log when retries are attempted and their outcomes.
  * Strategy for handling "no data found" from API (log as INFO, not necessarily an error if data legitimately doesn't exist).
  * Idempotency: Ensure re-running the script for the same season/player updates existing records or inserts new ones without duplication. This often involves checking if a record exists before inserting or using `ON CONFLICT DO UPDATE` in PostgreSQL.

---

### 4. Feature Name: basketball_reference_web_scraper Client Scripts (Module 3)

* **Feature Name:** Module 3: `basketball_reference_web_scraper` Client Scripts
* **Intent/Goal of Feature:** To systematically fetch deep historical data, especially for BAA/NBL/ABA eras, awards, transactions, and other detailed player/team information from Basketball-Reference.com using the `basketball_reference_web_scraper` Python library.
* **APIs Involved (Internal/External):**
  * Internal: `basketball_reference_web_scraper` library functions, `pandas` for data manipulation, `psycopg2` (via `db_manager.py`/`db_utils.py`).
  * External: Basketball-Reference.com (abstracted by the library).
* **Detailed Technical Requirements/User Stories:**
  * Utilize library functions to fetch various data types:
    * Player season statistics (per game, totals, advanced) for NBA, BAA, NBL, ABA.
    * Team box scores.
    * Season schedules.
    * Player game logs.
    * Award data (MVP, All-NBA, etc.) including voting details if available.
    * Draft results.
    * Player biographical details (height, weight, birth date, college).
    * Transaction details (if exposed by the library).
  * Iterate through seasons (e.g., 1947 to 2025), team abbreviations (as used by Basketball-Reference), and player identifiers (Basketball-Reference specific IDs, or names if the library handles resolution).
  * The library often requires season end year (e.g., 2023 for 2022-23 season).
  * Transform data from the library's output structures (often JSON or lists of dictionaries) into pandas DataFrames, then map to the PostgreSQL schema.
  * Handle potential data type conversions and missing values.
  * Load data into appropriate PostgreSQL tables, with logic for inserts or updates (idempotency).
  * Adhere to polite scraping practices; the library might handle some rate limiting, but monitor and add explicit delays if necessary (configurable).
  * Log all major operations, data fetched, and errors.
* **Overall System Architecture Context:** A key data collection module, particularly for historical data and details not in `nba_api`. Feeds data into the `Data Cleaning and Transformation Layer (Module 5)`.
* **Database Schema Impact:** Populates tables like `Players` (bio, historical stats), `PlayerSeasonTotals`, `PlayerGameStats`, `TeamSeasonTotals`, `Games`, `Schedules`, `Awards`, `PlayerAwards`, `DraftPicks`, `Transactions`.
* **API Endpoint Specifications (Internal Script Structure - `data_collection/bball_ref_client.py`):**
  * `Key function definitions`:
    * `fetch_player_season_stats_br(season_end_year, stat_type='PER_GAME')`
    * `fetch_player_game_logs_br(player_identifier, season_end_year)`
    * `fetch_season_schedule_br(season_end_year)`
    * `fetch_awards_br(season_end_year)`
    * `process_br_data(data, target_table_name, column_mapping)`
    * A main orchestrator function: `run_bball_ref_collection(seasons_to_fetch, specific_players=None)`
  * `Input parameters`: Season end years, player identifiers (names or BR IDs), library-specific parameters (e.g., `data_format`). Configurable delay.
  * `Expected output/return values`: DataFrames, status messages.
  * `Core logic`:
    * Loop through seasons/players.
    * Call `basketball_reference_web_scraper` functions (e.g., `from basketball_reference_web_scraper.client import regular_season_player_box_scores`).
    * The library often writes directly to files or returns JSON. Adapt to read this output.
    * If the library doesn't have explicit delays, add `time.sleep(delay)` (configurable, e.g., `bball_ref_delay_seconds`).
    * Transform data to match PostgreSQL schema.
    * Use `db_manager.py` for loading data.
  * `Error handling`:
    * Try-except blocks for library-specific exceptions, network errors if the library doesn't abstract them fully, parsing errors from library output.
    * Log errors with context (season, player, function called).
    * Handle cases where data is legitimately missing for older seasons/leagues gracefully (log as INFO).
  * `Rate limiting/politeness considerations`: Crucial. Even with a library, monitor request frequency. Use a configurable delay (e.g., 3-5 seconds) if direct library calls are rapid. The library's own mechanisms should be understood.
* **CRUD Operations:** Create and Update (upsert logic).
* **Security Considerations:** Adherence to Basketball-Reference.com's `robots.txt` and ToS, even when mediated by a library.
* **Testing Strategy Notes:**
  * Unit test transformation functions with sample JSON/data structures returned by the library (mocked).
  * Mock library calls to test script logic without hitting the live site.
  * Integration test: Run script for a small subset (e.g., one season, one player type) and verify DB.
  * Test handling of empty results or errors from the library.
* **Data Management & Error Logging Notes:**
  * Log functions called, parameters, and records retrieved/processed.
  * Log missing data points or seasons where data is unavailable.
  * Strategy for retrying failed requests (if library doesn't handle it).
  * Ensure idempotency for re-runs.

---

### 5. Feature Name: Custom Web Scraping Scripts (Module 4 - Targeted)

* **Feature Name:** Module 4: Custom Web Scraping Scripts (Targeted)
* **Intent/Goal of Feature:** To develop and use custom Python scripts (`requests`, `BeautifulSoup4`, potentially `Scrapy`) to extract specific data points not available or adequately covered by Kaggle datasets or the `nba_api` and `basketball_reference_web_scraper` libraries. This includes player pronunciations, detailed nicknames, RSCI ranks, full transaction narratives, HoF methodology text, specific referee details, and Spotrac contract details.
* **APIs Involved (Internal/External):**
  * Internal: `requests` (HTTP calls), `BeautifulSoup4` / `lxml` (HTML parsing), `Scrapy` (for complex multi-page/project-based scraping), `pandas`, `psycopg2`.
  * External: Websites like Basketball-Reference.com (for specifics not in library), Spotrac.com, NBA.com (fallback).
* **Detailed Technical Requirements/User Stories:**
  * For each target data point and source website:
    * Identify specific URLs or URL patterns to fetch.
    * Develop Python functions to fetch HTML content using `requests`.
    * Implement strict adherence to `robots.txt` of each site.
    * Set a descriptive User-Agent string (configurable).
    * Implement significant, configurable delays (e.g., `custom_scraper_delay_seconds` in `config.ini`, default 3-5 seconds, potentially site-specific delays) between requests to the same domain.
    * Use `BeautifulSoup4` (or `lxml` with `requests`) to parse HTML and extract target data elements using CSS selectors or XPath expressions.
    * For more complex scenarios (e.g., following pagination, managing multiple item types from a site), `Scrapy` framework might be used, defining spiders, items, and pipelines.
    * Clean and structure the extracted data.
    * Transform data to match the PostgreSQL schema.
    * Load data into PostgreSQL tables (upsert logic).
  * Specific targets:
    * Basketball-Reference: Player Pronunciation, Nicknames (if not fully covered by library), Recruiting Rank (RSCI), Relatives (if not in library), detailed salary/contract term explanations, specific statistical definitions.
    * Spotrac.com: Detailed current/historical player contract details (salary breakdowns, length, options, guaranteed money), trade exceptions.
    * NBA.com (fallback): Player pronunciation audio links, specific transaction log views if not in API.
  * Implement robust error handling for network issues, HTTP errors, changes in website structure (parsing errors).
  * Log all scraping activities, URLs fetched, data extracted, and errors.
* **Overall System Architecture Context:** Supplements other data collection modules by filling specific gaps. Data flows to `Data Cleaning and Transformation Layer (Module 5)`.
* **Database Schema Impact:** Populates specific fields in `Players` (pronunciation, nicknames, RSCI, relatives_info JSONB), `Contracts` (detailed terms from Spotrac), `PlayerSalaries`, `Transactions` (narratives), `StatDefinitions`, `HoFProbMethodology` (text), `Referees` (details).
* **API Endpoint Specifications (Internal Script Structure - `data_collection/custom_scrapers/`):**
  * Each scraper might be a separate file (e.g., `bball_ref_custom_scraper.py`, `spotrac_scraper.py`).
  * `Key function definitions` (example for `spotrac_scraper.py`):
    * `fetch_page_content(url, headers, delay)`
    * `parse_player_contract_page(html_content, player_id)`
    * `extract_salary_breakdown(soup_element)`
    * `run_spotrac_player_contracts_scraper(player_list)`
  * `Input parameters`: Player names/IDs (to form URLs), team names, season years. Delays, User-Agent from `config.ini`.
  * `Expected output/return values`: Structured data (dictionaries, lists of objects), DataFrames.
  * `Core logic`:
    * Construct target URLs.
    * Make HTTP GET requests with `requests.get(url, headers=...)`.
    * `time.sleep(delay)`.
    * Parse with `BeautifulSoup(html_content, 'lxml')`.
    * Use `soup.select('css_selector')` or `soup.find_all()` to locate data.
    * Extract text, attributes. Clean extracted strings.
    * Structure into dictionaries or DataFrames.
    * Load via `db_manager.py`.
  * `Error handling`:
    * Try-except for `requests.exceptions.RequestException`, `AttributeError` (if HTML structure changes and selectors fail).
    * Check HTTP status codes.
    * Log errors with URL and selector if parsing fails.
    * Consider a "dead letter queue" or logging for URLs that consistently fail parsing for later manual review.
  * `Rate limiting/politeness considerations`: Paramount. Strict adherence to `robots.txt`. Configurable delays (3-5+ seconds). Descriptive User-Agent. Cache pages locally during development to avoid re-hitting sites.
* **CRUD Operations:** Create and Update (upsert).
* **Security Considerations:** Primarily ethical scraping. Do not overload servers. Be transparent with User-Agent.
* **Testing Strategy Notes:**
  * Unit test parsing functions with saved sample HTML files.
  * Mock `requests.get` to return sample HTML, testing scraper logic without live requests.
  * Integration test with one or two live pages (with long delays) to ensure selectors work, then verify DB.
  * Regularly check if selectors are still valid, as website changes will break scrapers.
* **Data Management & Error Logging Notes:**
  * Log URL fetched, success/failure, number of items extracted.
  * If a scraper fails due to site structure change, log prominently and potentially send an alert/email if automation is advanced.
  * Store selectors in a configurable way if possible, or at least clearly comment them in code.

---

### 6. Feature Name: Data Cleaning and Transformation Layer (Module 5)

* **Feature Name:** Module 5: Data Cleaning and Transformation Layer
* **Intent/Goal of Feature:** To provide a centralized set of Python/pandas functions and processes to clean, standardize, transform, and resolve conflicts in data collected from all sources (Kaggle, `nba_api`, `bball_ref_scraper`, custom scrapers) before final loading into PostgreSQL.
* **APIs Involved (Internal/External):**
  * Internal: `pandas` (for DataFrame operations), `NumPy` (for numerical operations), potentially `fuzzywuzzy` (for string matching), custom mapping dictionaries/files. Interacts with dataframes passed from collection modules.
* **Detailed Technical Requirements/User Stories:**
  * **Missing Value Handling:**
    * Consistently represent missing data as `None` in Python, which translates to `NULL` in PostgreSQL.
    * Document any specific imputation logic if ever applied (for MVP, primarily use `NULL`).
  * **Data Type Consistency:**
    * Ensure all data destined for specific DB columns conforms to the target data type (e.g., convert strings to `int`, `float`, `datetime`). Log conversion errors.
  * **Text Normalization:**
    * Standardize text case (e.g., lowercase for identifiers, proper case for names where appropriate).
    * Trim leading/trailing whitespace.
    * Remove or standardize special characters if necessary.
  * **Entity Name Standardization (Players, Teams):**
    * Implement logic in `name_standardizer.py`.
    * Use predefined mapping tables (e.g., CSV or DB table) for known variations (e.g., "Metta World Peace" -> "Ron Artest" for a certain period, team name changes before franchise ID mapping).
    * Employ fuzzy matching (e.g., `fuzzywuzzy.process.extractOne`) against a canonical list of player/team names (derived from a primary source).
    * Log names that cannot be matched with high confidence to the `unmatched_player_names_review` table (as discussed in Phase 1 Q&A).
    * Ensure standardized names map to canonical `player_id` and `team_id` (franchise_id).
  * **Data Conflict Resolution:**
    * Implement logic in `conflict_resolver.py`.
    * Apply defined source precedence: 1. NBA.com API (for "current" data as defined), 2. Basketball-Reference.com (for historical/detailed), 3. Kaggle, 4. Other custom scraped.
    * When conflicts arise for the same data point (e.g., points scored in a specific game), select the value from the higher-priority source.
    * Log the conflict, the chosen value, and the discarded value(s) with their sources (e.g., to `data_source_conflicts` table or JSONB column as discussed).
  * **Unit Standardization:** Ensure stats are in consistent units (e.g., height in inches/cm, weight in lbs/kg - decide on one and convert).
  * **Schema Mapping:** Ensure DataFrames from various sources have columns named and ordered correctly to match target PostgreSQL tables.
* **Overall System Architecture Context:** This is a crucial intermediate layer. Data from collection modules (1-4) passes through this layer before being loaded by `Data Loading and ETL Orchestration (Module 6)`.
* **Database Schema Impact:** Does not directly define tables, but prepares data for insertion into virtually all tables. Populates `unmatched_player_names_review` and `data_source_conflicts`. Relies on canonical `Players` and `Teams` tables for standardization.
* **API Endpoint Specifications (Internal Script Structure - `data_processing/`):**
  * `cleaner.py`:
    * `handle_missing_values(df, strategy_dict)`
    * `convert_data_types(df, type_mapping_dict)`
    * `normalize_text_column(series, case_type='lower')`
  * `transformer.py`:
    * `map_columns(df, column_mapping_dict)`
    * `standardize_units(df, unit_conversion_rules)`
  * `name_standardizer.py`:
    * `load_canonical_names(db_conn, entity_type='player')`
    * `standardize_player_name(name_series, canonical_names_df, fuzzy_threshold=90)`
    * `log_unmatched_name(original_name, source, potential_matches, review_table_conn)`
  * `conflict_resolver.py`:
    * `resolve_conflicts(primary_df, secondary_df, join_keys, column_list, primary_source_name, secondary_source_name)`
  * `Input parameters`: Pandas DataFrames, configuration for cleaning/transformation rules, database connections for mappings/logging.
  * `Expected output/return values`: Cleaned and transformed pandas DataFrames.
  * `Core logic`: Extensive use of pandas DataFrame operations (`.apply()`, `.map()`, `.astype()`, `.fillna()`, `.merge()`). Conditional logic for conflict resolution.
  * `Error handling`: Log issues during cleaning/transformation (e.g., type conversion failure for a specific value, unresolvable name). Decide if an error is critical enough to stop processing for that row/DataFrame.
* **CRUD Operations:** Reads from mapping tables, writes to review/conflict logging tables. Prepares data for C/U operations in the loading stage.
* **Security Considerations:** N/A.
* **Testing Strategy Notes:**
  * Extensive unit tests for each function in `cleaner.py`, `transformer.py`, `name_standardizer.py`, `conflict_resolver.py` with diverse sample DataFrames covering edge cases, missing data, conflicting data, various name formats.
  * Test fuzzy matching with known variations and deliberately ambiguous names.
  * Verify that conflict resolution correctly applies precedence rules.
* **Data Management & Error Logging Notes:**
  * Log summary statistics of cleaning (e.g., % missing values handled, number of names standardized, number sent to review, number of conflicts resolved).
  * Ensure transformations are traceable.

---

### 7. Feature Name: Data Loading and ETL Orchestration (Module 6)

* **Feature Name:** Module 6: Data Loading and ETL Orchestration
* **Intent/Goal of Feature:** To manage the overall execution flow of the data collection, processing, and loading pipeline. This includes calling the various modules in the correct order, handling dependencies, ensuring data is loaded efficiently and correctly into PostgreSQL, and implementing comprehensive logging for the entire ETL process. Scripts should be idempotent.
* **APIs Involved (Internal/External):**
  * Internal: Functions from Modules 1-5, `db_manager.py` (for loading data, executing DDL if needed), Python's `subprocess` (if orchestrating external scripts, though direct function calls are preferred for Python modules), `logging` module.
  * External: PostgreSQL.
* **Detailed Technical Requirements/User Stories:**
  * **Orchestration (`main_etl.py`):**
    * Define the sequence of operations for the entire ETL pipeline (e.g., setup schema (if first run), ingest Kaggle, fetch from `nba_api`, fetch from `bball_ref_scraper`, run custom scrapers, clean/transform all collected data, load into final tables).
    * Handle dependencies between tasks (e.g., player IDs from `nba_api` might be needed for other scrapers).
    * Allow for configurable runs (e.g., full historical backfill vs. daily update for current season).
  * **Data Loading (`db_manager.py` / `db_utils.py`):**
    * Provide efficient functions for loading pandas DataFrames into PostgreSQL tables.
    * Implement upsert logic (INSERT ON CONFLICT DO UPDATE/NOTHING) to ensure idempotency, preventing duplicate records on re-runs and allowing updates to existing records.
    * Handle batching for large datasets to manage memory and database load.
  * **Logging:**
    * Implement centralized, comprehensive logging for the entire ETL process using Python's `logging` module.
    * Log start and end of the entire ETL run, and of each major module/task.
    * Log records processed, inserted, updated, and rejected at each stage.
    * Log errors and warnings from all modules.
    * Logs should go to both console (configurable level) and a file (`logs/etl.log`).
  * **Configuration:**
    * The orchestrator should read parameters from `config.ini` (e.g., seasons to process, API keys/delays, mode of operation).
  * **Error Handling:**
    * The master script should decide how to handle errors from sub-modules: halt entire pipeline (default for critical errors) or log and continue (for non-critical, independent tasks).
* **Overall System Architecture Context:** This is the "brain" of the ETL pipeline, coordinating all other modules. `main_etl.py` is the entry point for running the data collection and population process.
* **Database Schema Impact:** Responsible for triggering the loading of data into all target tables via `db_manager.py`. May also trigger initial schema setup.
* **API Endpoint Specifications (Internal Script Structure):**
  * `main_etl.py`:
    * `main()` function: Parses arguments (e.g., `--full-load`, `--update-season YYYY`), loads config.
    * Functions for each major stage: `run_kaggle_ingestion_stage()`, `run_nba_api_stage()`, `run_data_processing_stage()`, `run_final_load_stage()`.
  * `database/db_manager.py` (or `db_utils.py`):
    * `bulk_upsert_df(df, table_name, unique_constraint_cols, db_conn)`: Efficiently upserts a DataFrame.
    * `execute_batch(cursor, query, data_list)`
  * `Input parameters`: Configuration settings, command-line arguments.
  * `Expected output/return values`: Status messages, log files.
  * `Core logic`: Sequential calls to module functions. Conditional logic based on run mode. Error aggregation.
  * `Error handling`: Top-level try-except block in `main_etl.py` to catch unhandled exceptions from modules. Log and exit gracefully.
* **CRUD Operations:** Orchestrates Create and Update operations on the database via data loading functions.
* **Security Considerations:** Secure handling of DB credentials passed to `db_manager.py`.
* **Testing Strategy Notes:**
  * Test `main_etl.py` with different run configurations (e.g., full load vs. update).
  * Mock individual ETL modules to test the orchestrator's logic and error handling flow.
  * Integration test the entire pipeline with a very small data subset (e.g., one season, a few players) from start to finish.
  * Verify idempotency by running an update script multiple times and checking for data duplication or incorrect updates.
  * Check log file creation and content.
* **Data Management & Error Logging Notes:**
  * Master log file (`etl.log`) should be the single source of truth for ETL run status.
  * Implement log rotation if log files become very large over time.
  * Define clear success/failure criteria for an ETL run.

---

### 8. Feature Name: Basic Querying Capability

* **Feature Name:** Basic Querying Capability
* **Intent/Goal of Feature:** To ensure that the user (you) can connect to the populated PostgreSQL database using standard SQL clients (like DBeaver) or programmatically (via `psycopg2` in Python) and execute SQL queries to retrieve, analyze, and explore the collected historical NBA data.
* **APIs Involved (Internal/External):**
  * Internal: `psycopg2` (for programmatic access from Python if needed for ad-hoc scripts).
  * External: PostgreSQL server, SQL clients (e.g., DBeaver Community Edition, `psql`).
* **Detailed Technical Requirements/User Stories:**
  * The PostgreSQL database server must be running and accessible.
  * The `nba_data_db` database must exist and be populated with data from the ETL pipeline.
  * The dedicated database user must have `SELECT` privileges (at a minimum) on all relevant tables and views.
  * The user should be able to successfully connect to the database using DBeaver (or other preferred SQL client) with the correct credentials and connection parameters.
  * The user should be able to execute standard SQL `SELECT` queries against the tables.
  * Queries involving `JOIN`s across multiple tables (e.g., PlayerGameStats with Players and Games) should execute correctly and return expected results.
  * Basic filtering (`WHERE`), sorting (`ORDER BY`), and aggregation (`GROUP BY`, `SUM`, `AVG`) should function as expected.
  * Indexes created during schema setup should support efficient query performance for common lookups.
* **Overall System Architecture Context:** This is the primary way the user interacts with the final output of the project – the database itself. It's the "read" part after all the "write" operations of the ETL.
* **Database Schema Impact:** Relies entirely on the populated schema. No changes are made by this "feature"; it's about access.
* **API Endpoint Specifications (N/A for direct querying):** For programmatic access via Python:
  * Standard `psycopg2` usage: `conn = psycopg2.connect(...)`, `cur = conn.cursor()`, `cur.execute("SELECT ...")`, `rows = cur.fetchall()`.
* **CRUD Operations:** Primarily Read (`SELECT` statements).
* **Security Considerations:** Ensure the query user has appropriate (e.g., read-only if not the ETL user) permissions. Protect database credentials.
* **Testing Strategy Notes:**
  * Manually connect with DBeaver and run a diverse set of test queries (simple selects, joins, aggregations, filtering).
  * Verify results against known data points or expectations.
  * Check query execution times for common queries to ensure indexes are effective (basic performance check).
  * Write a simple Python script using `psycopg2` to connect and fetch some data.
* **Data Management & Error Logging Notes:** Query errors will typically be reported by the SQL client or `psycopg2`. Database logs on the server might capture more details if needed.

---

### 9. Feature Name: Foundational Documentation (Initial Versions)

* **Feature Name:** Foundational Documentation (Initial Versions)
* **Intent/Goal of Feature:** To create essential documentation for the project, including a detailed Data Dictionary describing the database schema, and a comprehensive README.md file outlining the project, setup, and usage.
* **APIs Involved (Internal/External):** N/A (Documentation creation).
* **Detailed Technical Requirements/User Stories:**
  * **README.md (`README.md`):**
    * Must contain: Project Title, Core Idea/Overview.
    * Technology Stack used (Python version, PostgreSQL version, key libraries).
    * Detailed Setup Instructions: Environment setup (Python venv, library installation via `requirements.txt`), PostgreSQL installation and database/user creation, DBeaver connection.
    * Overview of the `Project File System Structure`.
    * Instructions on How to Run the ETL Pipeline (e.g., `python main_etl.py --full-load`, `python main_etl.py --update-season 2024`).
    * Link/Reference to the Data Dictionary (`docs/data_dictionary.md`).
    * Link/Reference to Query Examples (`docs/query_examples.sql`).
    * Brief Troubleshooting tips / FAQ section.
    * List of Data Sources used and Acknowledgements.
    * (Optional) Contact info or contribution guidelines if ever relevant.
  * **Data Dictionary (`docs/data_dictionary.md`):**
    * Must provide a detailed description for every table and every column in the PostgreSQL schema.
    * For each table:
      * Table Name
      * Brief Description of the table's purpose.
    * For each column within a table:
      * Column Name
      * Data Type (PostgreSQL specific)
      * Description (Purpose of the column, what it represents)
      * Source(s) (Which collection script/API endpoint primarily populates this? e.g., "nba_api: playergamelog", "Basketball-Reference: Player Season Totals page", "Kaggle: games.csv").
      * Constraints (PK, FK (referencing which table/column), UNIQUE, NOT NULL, CHECK).
      * Nullability (YES/NO).
      * Units (If applicable, e.g., "minutes", "points", "YYYY-MM-DD", "inches").
      * Examples (Illustrative values, e.g., "1610612737", "LeBron James", "2023-10-24").
      * Notes (Any specific context, historical changes affecting this column, calculation methods if not obvious, or how missing data is handled).
  * **ETL Process Documentation (`docs/etl_process.md`):**
    * High-level overview of the data flow from sources to database.
    * Brief description of each ETL module's role and its key inputs/outputs.
    * Diagram of the ETL pipeline (could be a text-based or Mermaid diagram).
  * **Historical Nuances (`docs/historical_nuances.md`):**
    * Initial notes on major rule changes or statistical definition changes that impact data interpretation across different eras (e.g., introduction of 3-point line, changes in block/steal tracking).
  * All documentation should be written in clear, unambiguous language and formatted for readability (e.g., using Markdown).
  * Documentation files should be version controlled in Git.
* **Overall System Architecture Context:** Provides essential information for understanding, using, and maintaining the entire system.
* **Database Schema Impact:** The Data Dictionary directly documents the schema.
* **API Endpoint Specifications (N/A):**
* **CRUD Operations:** Create and Update (documentation files).
* **Security Considerations:** N/A.
* **Testing Strategy Notes:**
  * Review documentation for clarity, completeness, and accuracy.
  * Verify that setup instructions in README.md are correct and lead to a working environment.
  * Cross-reference Data Dictionary with the actual implemented schema in PostgreSQL to ensure consistency.
* **Data Management & Error Logging Notes:** Documentation should be updated as the schema or ETL processes evolve.

---

This completes the detailed technical specifications for all MVP features and the proposed file system structure.
