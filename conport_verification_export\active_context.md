# Active Context
## Active Decisions Considerations
*   Prioritizing which additional MCP servers (e.g., `filesystem`, `fetch`, `sqlite`, `Playwright`) will provide the most immediate value for the upcoming ETL development stages.
*   Ensuring `config.ini` placeholders for Kaggle data are clear and will be updated by the user once data is downloaded.
*   Preparing for DDL script creation, ensuring adherence to `planning/design.md` for table structures and `planning/spec.md` for DDL file organization.

## Current Focus
*   Developing ETL orchestration script to utilize the database utilities in db_manager.py.
*   Planning implementation of data extraction components for the various data sources.
*   Updating Memory Bank (progress.md, techContext.md) with recent MCP server activities (PostgreSQL MCP server setup).
*   Analyzing planning/nbadb/mcplist.md to prioritize next MCP server installations for ETL development.
*   Debugging ETL pipeline: Investigating `ValueError` in `db_manager.py` (`load_df_to_table`) related to `psycopg2.extras.execute_values` and missing '%s' placeholder.
*   Debugging ETL pipeline: Verifying fix for timezone comparison error in `etl_orchestrator.py` (`_fetch_bball_ref_games`).

## Important Patterns Preferences
*   Adherence to the Memory Bank structure and update workflow defined in `.clinerules/memory-bank.md`.
*   Systematic execution of setup steps as outlined in `planning/build.md`.
*   Iterative refinement of configuration files (`config.ini`, `cline_mcp_settings.json`) based on project documents, tool documentation, and troubleshooting.

## Learnings Project Insights
*   The `planning/*.md` files provide a very detailed blueprint, crucial for systematic setup.
*   PostgreSQL permissions require explicit schema grants (`GRANT USAGE, CREATE ON SCHEMA public`) beyond database-level grants for table creation by a user.
*   User's shell environment (likely PowerShell based on error messages) requires individual command execution rather than chaining with `&&`.
*   Confirming each step of environment setup methodically ensures a stable base for development.
*   MCP Server Setup on Windows with npx: Using full path to npx-cli.js with node is more reliable.
*   Placeholders in `cline_mcp_settings.json` might not always be substituted; direct values can be more robust.
*   SQL linters need correct dialect configuration.
*   Pandas DataFrame date filtering requires consistent timezone awareness between columns (e.g., `datetime64[ns, UTC]`) and comparison values (e.g., naive `datetime`) to prevent `TypeError`. Ensure comparison dates are also UTC-aware.
*   The `psycopg2.extras.execute_values` function's error `ValueError: the query doesn't contain any '%s' placeholder` can be misleading if the query string syntactically includes `%s`. Debug logging of the exact query and data passed to the function is crucial for diagnosis.
*   Configuration file parsing (e.g., `configparser` for `.ini` files) can be sensitive to non-standard value formats. Comments or additional text appended to values (like `delay_seconds = 5 ; comment`) can lead to parsing warnings or the use of default values.

## Open Issues Blockers Next Steps
*   Decision: Prioritize and install next set of MCP servers (e.g., filesystem, fetch, sqlite, Playwright) from planning/nbadb/mcplist.md.
*   Task: Develop ETL orchestration script to coordinate data extraction, transformation, and loading processes.
*   Task: Implement data extraction components for planned data sources (Kaggle datasets, nba-api, basketball-reference-web-scraper).
*   Task: Re-run ETL pipeline to capture debug logs for `ValueError: the query doesn't contain any '%s' placeholder` in `db_manager.py`.
*   Task: Analyze new ETL logs to diagnose and fix the `%s` placeholder issue in `db_manager.py`.
*   Action: Manually correct `delay_seconds` in `config.ini` to be a plain integer to resolve warning during Basketball Reference processing.

## Recent Changes
*   Successfully executed DDL scripts (001_create_leagues_seasons.sql, 002_create_franchises_teams.sql) to create/update core schema tables (leagues, seasons, franchises, teams) using db_manager.py.
*   Verified core schema tables (leagues, seasons, franchises, teams) and columns in PostgreSQL via SQL queries. The schema matches the DDL definitions, including recent additions to the teams table.
*   Successfully configured and tested the @modelcontextprotocol/server-postgres MCP server. Configuration added to cline_mcp_settings.json.
*   Researched and documented a list of 50 potentially useful MCP servers in planning/nbadb/mcplist.md.
*   SQL linter errors for db/nba_data_db.sql investigated and appear resolved.
*   Completed: PostgreSQL 17 server, database (nba_data_db), and user (user/password) setup.
*   Completed: Python 3.10.11, Git, venv, and requirements.txt installation.
*   Completed: config.ini and .env file setup with database credentials.
*   Applied fix in `etl_orchestrator.py` (`_fetch_bball_ref_games`) to make target dates timezone-aware (UTC), addressing `TypeError` during date comparison with Basketball Reference data.
*   Added debug logging in `db_manager.py` (`load_df_to_table`) to inspect `insert_query` and data chunk details for `psycopg2.extras.execute_values`.

## Current Work Focus Detailed
ETL Pipeline Debugging: Addressing NumericValueOutOfRange error for Players.csv (target table eoin_players) by ensuring correct transformation logic is applied. Recent focus includes debugging psycopg2.extras.execute_values ValueError related to missing '%s' placeholder in db_manager.py, and fixing timezone comparison errors in etl_orchestrator.py.

## Recent Changes Comprehensive
*   etl/etl_orchestrator.py Update (Players - Corrected transform condition): Changed elif file_name == 'Players.csv': to elif file_name == 'Players': in _transform_eoin_moore_data to ensure player-specific transformations are applied
*   config.ini Update (Players - Corrected Keys): Adjusted keys in [ColumnRenames_Players] to match standardized CSV column names
*   config.ini Update (LeagueSchedule): Added [ColumnRenames_LeagueSchedule] section
*   config.ini Update (Games - Corrected Casing): Adjusted casing in [ColumnRenames_Games] section
*   config.ini Update (TeamStatistics - Corrected): Replaced [ColumnRenames_TeamStatistics] section
*   ETL Pipeline Implementation: Core components etl_orchestrator.py, extractors.py, transformers.py, loaders.py, run_etl.py, schema_utils.py are functional
*   Database Management: db_manager.py provides robust PostgreSQL connection management, DDL execution, and DataFrame loading
*   Data Storage Structure: data/ directory with subfolders established

## Next Steps Detailed
*   Test ETL Pipeline (Players Fix): Execute python etl/run_etl.py --sources kaggle_eoin_moore --tables eoin_players to verify Players.csv NumericValueOutOfRange fix
*   Monitor ETL Logs: Check logs/etl.log and timestamped logs for successful processing
*   Address Further ETL Issues: Investigate transformation logic or data if errors persist
*   Validate Data: Perform basic data validation queries once all Eoin Moore datasets load successfully

## Tool Usage Patterns
{'etl_run_etl_py': 'Command-line entry point for ETL pipeline. Parses arguments, sets up logging, initiates ETLOrchestrator', 'etl_orchestrator_py': 'Central orchestrator script. Manages overall ETL workflow, schema initialization, data source processing', 'extractors_py': 'Contains classes for extracting data from various sources (KaggleCSVExtractor, NBAAPIExtractor, WyattKaggleCSVExtractor)', 'transformers_py': 'Contains classes for transforming extracted data (KaggleTransformer, NBAAPITransformer, WyattFranchiseTeamTransformer)', 'loaders_py': 'Contains classes for loading transformed data into database (PostgreSQLLoader)', 'db_manager_py': 'Handles all direct PostgreSQL interactions including DDL execution and data loading', 'config_ini_env': 'Used by all modules for configuration parameters and sensitive credentials', 'logs_directory': 'Stores ETL log files (etl_TIMESTAMP.log, etl.log)'}

## Etl Success Milestones
*   PlayerStatistics ETL: Successfully processed 1,627,146 rows after [ColumnRenames_PlayerStatistics] added to config.ini
*   TeamStatistics ETL: Successfully processed after [ColumnRenames_TeamStatistics] corrected
*   Games ETL (game_logs): Successfully processed after [ColumnRenames_Games] corrected for casing
*   LeagueSchedule ETL (eoin_league_schedule): Successfully processed after [ColumnRenames_LeagueSchedule] added

