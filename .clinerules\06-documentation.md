# Documentation Standards

## Code Comments
- Use comments sparingly to explain the *why* behind complex or non-obvious code, not the *what*.
- Ensure comments are kept up-to-date with code changes.

## External Documentation
- **Requirement:** When adding or significantly modifying features, identify and mention the need to update relevant documentation (e.g., files in `/docs`, `README.md`). *<PERSON><PERSON> should prompt the user to do this or offer to draft updates.*
- **Requirement:** Maintain `CHANGELOG.md` entries for significant changes.

## Architecture Decision Records (ADRs)
- **Requirement:** Create ADRs in `/docs/adr` (following `/docs/adr/template.md`) for:
    - Major dependency changes.
    - Architectural pattern changes.
    - New integration patterns.
    - Database schema changes.
- *<PERSON><PERSON> should identify when an ADR is likely needed based on the task.*