# Expected location: c:\Users\<USER>\Projects\nbadb\Database\database\db_manager.py
import psycopg2
import os
import logging
from dotenv import load_dotenv # For loading .env file for credentials
import pandas as pd
from psycopg2 import sql # Import sql module for safer SQL formatting

# Load environment variables from .env file, if present
load_dotenv()

# Configure basic logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(module)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, db_config=None):
        """
        Initializes the DatabaseManager.
        db_config: A dictionary with database connection parameters
                   (host, port, dbname, user, password).
                   If None, it loads from environment variables.
        """
        if db_config:
            self.db_config = db_config
        else:
            self.db_config = self._load_config_from_env()
        
        if not all(k in self.db_config for k in ['dbname', 'user', 'password', 'host']):
            logger.error("Database configuration is incomplete. Ensure DB_NAME, DB_USER, DB_PASSWORD, DB_HOST are set.")
            raise ValueError("Incomplete database configuration.")

        self.conn = None
        self.cursor = None

    def _load_config_from_env(self):
        """Loads database configuration from environment variables."""
        config = {
            "dbname": os.getenv("DB_NAME"),
            "user": os.getenv("DB_USER"),
            "password": os.getenv("DB_PASSWORD"),
            "host": os.getenv("DB_HOST"),
            "port": os.getenv("DB_PORT", 5432) # Default PostgreSQL port
        }
        logger.info("Loaded database configuration from environment variables.")
        return config

    def connect(self):
        """Establishes a connection to the PostgreSQL database."""
        if self.conn is None or self.conn.closed:
            try:
                logger.info(f"Attempting to connect to database: {self.db_config['dbname']} on {self.db_config['host']}...")
                self.conn = psycopg2.connect(**self.db_config)
                self.cursor = self.conn.cursor()
                logger.info("Successfully connected to the database.")
            except psycopg2.OperationalError as e:
                logger.error(f"Database connection failed: {e}")
                raise
            except Exception as e:
                logger.error(f"An unexpected error occurred during database connection: {e}")
                raise

    def disconnect(self):
        """Closes the database connection."""
        if self.cursor:
            self.cursor.close()
            self.cursor = None
        if self.conn:
            self.conn.close()
            self.conn = None
            logger.info("Database connection closed.")

    def execute_query(self, query, params=None, fetch_mode=None):
        """
        Executes a given SQL query.

        Args:
            query (str): The SQL query string.
            params (tuple, optional): Parameters for the query. Defaults to None.
            fetch_mode (str, optional): 'one' for fetchone(), 'all' for fetchall(). 
                                      If None, query is executed without fetching (e.g., INSERT, UPDATE, DELETE).
                                      Defaults to None.

        Returns:
            Result of fetchone()/fetchall() if fetch_mode is 'one'/'all'.
            Cursor rowcount for DML statements if fetch_mode is None.
            None otherwise or if an error occurs (error is logged and re-raised).
        
        Raises:
            psycopg2.Error: If any database error occurs during execution.
        """
        if self.conn is None or self.conn.closed:
            self.connect()
        
        try:
            self.cursor.execute(query, params)
            self.conn.commit() # Commit after each successful execution for now
            logger.debug(f"Executed query: {query[:150]}...")

            if fetch_mode == 'one':
                return self.cursor.fetchone()
            elif fetch_mode == 'all':
                return self.cursor.fetchall()
            else: # For DML or DDL statements that don't return rows directly
                return self.cursor.rowcount 
        except psycopg2.Error as e:
            if self.conn: # Check if conn exists before rollback
                self.conn.rollback()
            logger.error(f"Error executing query: {e}\nQuery: {query}\nParams: {params}")
            raise

    def execute_ddl_scripts(self, ddl_scripts_dir):
        """
        Executes all .sql DDL scripts found in the given directory,
        in alphabetical (and thus numerical prefix) order.

        Args:
            ddl_scripts_dir (str): Path to the directory containing DDL .sql files.
        
        Raises:
            FileNotFoundError: If the ddl_scripts_dir does not exist.
            psycopg2.Error: If any error occurs during DDL execution.
        """
        if not os.path.isdir(ddl_scripts_dir):
            logger.error(f"DDL scripts directory not found: {ddl_scripts_dir}")
            raise FileNotFoundError(f"DDL scripts directory not found: {ddl_scripts_dir}")

        scripts = sorted([f for f in os.listdir(ddl_scripts_dir) if f.endswith(".sql")])
        
        if not scripts:
            logger.info(f"No .sql DDL scripts found in {ddl_scripts_dir}.")
            return

        logger.info(f"Found DDL scripts to execute: {scripts}")
        
        if self.conn is None or self.conn.closed:
            self.connect()

        try:
            for script_name in scripts:
                script_path = os.path.join(ddl_scripts_dir, script_name)
                with open(script_path, 'r', encoding='utf-8') as f:
                    ddl_content = f.read()
                
                logger.info(f"Executing DDL script: {script_name}...")
                self.cursor.execute(ddl_content) # psycopg2 can handle multi-statement strings if valid
                self.conn.commit()
                logger.info(f"Successfully executed DDL script: {script_name}")
            logger.info("All DDL scripts executed successfully.")
        except (psycopg2.Error, OSError) as e:
            if self.conn:
                self.conn.rollback()
            logger.error(f"Error during DDL script execution ({script_name if 'script_name' in locals() else 'N/A'}): {e}")
            raise

    def load_df_to_table(self, df: pd.DataFrame, table_name: str, if_exists: str = 'append', chunksize: int = None, unique_constraint_cols: list = None):
        """
        Loads a pandas DataFrame into a PostgreSQL table.

        Args:
            df (pd.DataFrame): DataFrame to load.
            table_name (str): Name of the target table.
            if_exists (str): {'append', 'replace', 'fail'}. Default 'append'.
                             - append: Insert new records.
                             - replace: Drop table and recreate it with new data.
                             - fail: Raise an error if table exists.
            chunksize (int, optional): Rows to write in batches. Default 1000.
            unique_constraint_cols (list, optional): List of column names that form a unique constraint.
                                                    Used for ON CONFLICT DO UPDATE logic when appending.
        
        Returns:
            int: Number of rows inserted or updated.
        
        Raises:
            ValueError: If invalid arguments are provided.
            psycopg2.Error: If database errors occur during execution.
        """
        if df.empty:
            logger.info(f"Empty DataFrame provided, no data to load into table '{table_name}'.")
            return 0
        
        if if_exists not in ['append', 'replace', 'fail']:
            raise ValueError("if_exists parameter must be one of: 'append', 'replace', 'fail'")
        
        if self.conn is None or self.conn.closed:
            self.connect()
        
        # Check if table exists
        table_exists = self._check_table_exists(table_name)
        
        # Handle 'fail' option
        if table_exists and if_exists == 'fail':
            raise ValueError(f"Table '{table_name}' already exists and if_exists is set to 'fail'.")
        
        # Handle 'replace' option
        if table_exists and if_exists == 'replace':
            logger.info(f"Dropping existing table '{table_name}' (if_exists='replace').")
            self.execute_query(f"DROP TABLE IF EXISTS {table_name}")
            table_exists = False
        
        # If table doesn't exist, we need to create it
        if not table_exists:
            logger.info(f"Creating table '{table_name}' based on DataFrame schema.")
            create_table_query = self._df_to_create_table_query(df, table_name)
            self.execute_query(create_table_query)
        
        # Prepare data for insertion
        from psycopg2.extras import execute_values
        
        # Identify numeric columns for proper handling
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        
        # Replace NaN values with None for SQL NULL
        df_copy = df.copy()
        for col in df.columns:
            if col in numeric_cols:
                df_copy[col] = df_copy[col].apply(lambda x: None if pd.isna(x) else x)
            else:
                df_copy[col] = df_copy[col].apply(lambda x: None if pd.isna(x) else str(x))
        
        # Prepare the INSERT query
        columns = ', '.join([f'"{col}"' for col in df.columns])
        insert_query = f"INSERT INTO {table_name} ({columns}) VALUES %s"
        
        # Add ON CONFLICT clause if unique constraint columns are provided
        if unique_constraint_cols and if_exists == 'append':
            constraint_cols_str = ', '.join([f'"{col}"' for col in unique_constraint_cols])
            update_cols = [col for col in df.columns if col not in unique_constraint_cols]
            
            if update_cols:
                update_cols_str = ', '.join([f'"{col}"=EXCLUDED."{col}"' for col in update_cols])
                insert_query += f" ON CONFLICT ({constraint_cols_str}) DO UPDATE SET {update_cols_str}"
            else:
                insert_query += f" ON CONFLICT ({constraint_cols_str}) DO NOTHING"
        
        # Convert DataFrame to list of tuples
        data_tuples = [tuple(x) for x in df_copy.to_numpy()]
        chunksize = chunksize or 1000
        total_rows = 0
        
        try:
            logger.info(f"Loading {len(df)} rows into table '{table_name}'...")
            
            # Process in chunks to avoid memory issues with large DataFrames
            for i in range(0, len(data_tuples), chunksize):
                chunk = data_tuples[i:i+chunksize]
                logger.debug(f"Executing load_df_to_table with query: '{insert_query}'")
                logger.debug(f"Sample of data chunk (first 2 rows): {chunk[:2]}")
                execute_values(self.cursor, insert_query, chunk, page_size=chunksize)
                self.conn.commit()
                total_rows += len(chunk)
                logger.debug(f"Inserted/updated chunk of {len(chunk)} rows into '{table_name}' ({i+len(chunk)}/{len(df)} rows processed).")
            
            logger.info(f"Successfully loaded {total_rows} rows into table '{table_name}'.")
            return total_rows
            
        except psycopg2.Error as e:
            self.conn.rollback()
            logger.error(f"Error loading DataFrame into table '{table_name}': {e}")
            raise
    
    def _check_table_exists(self, table_name: str) -> bool:
        """
        Checks if a table exists in the database.
        
        Args:
            table_name (str): Name of the table to check. Can be schema-qualified (e.g., 'public.tablename').
            
        Returns:
            bool: True if table exists, False otherwise.
        """
        # Parse schema-qualified table names (e.g., 'public.tablename')
        if '.' in table_name:
            schema, table = table_name.split('.', 1)
        else:
            schema = 'public'  # Default schema
            table = table_name
        
        query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s 
            AND table_name = %s
        )
        """
        result = self.execute_query(query, (schema, table), fetch_mode='one')
        return result[0] if result else False
    
    def _df_to_create_table_query(self, df: pd.DataFrame, table_name: str) -> str:
        """
        Generates a CREATE TABLE query based on DataFrame schema.
        
        Args:
            df (pd.DataFrame): DataFrame to derive schema from.
            table_name (str): Name of the table to create.
            
        Returns:
            str: SQL CREATE TABLE query.
        """
        # Map pandas dtypes to PostgreSQL data types
        dtype_map = {
            'int64': 'INTEGER',
            'int32': 'INTEGER',
            'int16': 'SMALLINT',
            'int8': 'SMALLINT',
            'float64': 'DOUBLE PRECISION',
            'float32': 'REAL',
            'bool': 'BOOLEAN',
            'datetime64[ns]': 'TIMESTAMP',
            'timedelta64[ns]': 'INTERVAL',
            'object': 'TEXT'
        }
        
        # Generate column definitions
        columns = []
        for col_name, dtype in df.dtypes.items():
            pg_type = dtype_map.get(str(dtype), 'TEXT')
            columns.append(f'"{col_name}" {pg_type}')
        
        columns_str = (',\n    ').join(columns)
        create_query = f"""
        CREATE TABLE {table_name} (
            {columns_str}
        )
        """
        
        return create_query

    def truncate_table(self, table_name: str):
        """
        Truncates a specified table (deletes all rows).

        Args:
            table_name (str): The name of the table to truncate.
        
        Raises:
            psycopg2.Error: If any database error occurs during execution.
        """
        if not table_name:
            logger.error("Table name for truncate cannot be empty.")
            raise ValueError("Table name for truncate cannot be empty.")

        if self.conn is None or self.conn.closed:
            self.connect()
        
        # Using psycopg2.sql for safer table name formatting
        query = sql.SQL("TRUNCATE TABLE {table} CASCADE;").format(table=sql.Identifier(table_name))
        
        try:
            self.cursor.execute(query)
            self.conn.commit()
            logger.info(f"Successfully truncated table '{table_name}'.")
        except psycopg2.Error as e:
            if self.conn:
                self.conn.rollback()
            logger.error(f"Error truncating table '{table_name}': {e}")
            raise

    def __enter__(self):
        """Context manager enter: connect to the database."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit: disconnect from the database."""
        self.disconnect()

# Example of how it might be used (for illustration - ensure .env file is set up):
if __name__ == '__main__':
    # Create a .env file in the project root (e.g., c:\Users\<USER>\Projects\nbadb\Database\.env)
    # Example .env content:
    # DB_NAME=nba_data_db
    # DB_USER=your_db_user
    # DB_PASSWORD=your_db_password
    # DB_HOST=localhost
    # DB_PORT=5432

    # load_dotenv() is called at the top of the file. 
    # It will look for .env in the current working directory or parent directories.
    # If running 'python database/db_manager.py' from 'c:\Users\<USER>\Projects\nbadb\Database\',
    # it should find 'c:\Users\<USER>\Projects\nbadb\Database\.env'.

    try:
        with DatabaseManager() as dbm:
            # 1. Execute DDL Scripts
            script_dir = os.path.dirname(os.path.abspath(__file__))
            ddl_dir = os.path.join(script_dir, "schema_ddl")
            logger.info(f"Attempting to load DDL scripts from: {ddl_dir}")
            dbm.execute_ddl_scripts(ddl_dir)

            # 2. Example: Insert data and fetch it (commented out for this test)
            # leagues_to_insert = [
            #     ('National Basketball Association', 'NBA'),
            #     ('American Basketball Association', 'ABA')
            # ]
            # for name, abbr in leagues_to_insert:
            #     dbm.execute_query(
            #         "INSERT INTO public.leagues (league_name, league_abbreviation) VALUES (%s, %s) ON CONFLICT (league_name) DO NOTHING;", 
            #         (name, abbr)
            #     )
            # logger.info("Inserted/updated leagues.")

            # current_leagues = dbm.execute_query("SELECT * FROM public.leagues", fetch_mode='all')
            # if current_leagues:
            #     logger.info("Current leagues in database:")
            #     for league in current_leagues:
            #         logger.info(league)

            # 3. Example: Load DataFrame (using placeholder - commented out)
            # sample_data = {'col1': [1, 2, 3], 'col2': ['apple', 'banana', 'cherry']}
            # sample_df = pd.DataFrame(sample_data)
            # dbm.load_df_to_table(sample_df, 'test_pandas_table')

            logger.info("DDL execution test finished successfully.")

    except ValueError as ve:
        logger.error(f"Configuration error: {ve}")
    except psycopg2.Error as dbe:
        logger.error(f"A database error occurred: {dbe}")
    except FileNotFoundError as fnfe:
        logger.error(f"File error: {fnfe}")
    except Exception as e:
        logger.error(f"An unexpected error occurred in the example usage: {e}")
