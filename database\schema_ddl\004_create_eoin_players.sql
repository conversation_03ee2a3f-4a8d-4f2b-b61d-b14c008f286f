CREATE TABLE IF NOT EXISTS public.eoin_players (
    person_id BIGINT PRIMARY KEY,
    first_name TEXT,
    last_name TEXT,
    birthdate DATE,
    last_attended TEXT,
    country TEXT,
    height_inches INTEGER, -- Assuming height is in inches from '76.0'
    body_weight_lbs INTEGER, -- Assuming weight is in lbs
    is_guard BOOLEAN,
    is_forward BOOLEAN,
    is_center BOOLEAN,
    draft_year INTEGER,
    draft_round INTEGER,
    draft_number INTEGER,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Trigger for updated_at
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'trg_eoin_players_update_timestamp'
        AND tgrelid = 'public.eoin_players'::regclass
    ) THEN
        -- Ensure the function exists before creating the trigger
        IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'fn_update_timestamp') THEN
            CREATE OR REPLACE FUNCTION fn_update_timestamp()
            RETURNS TRIGGER AS $func$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $func$ LANGUAGE plpgsql;
        END IF;

        CREATE TRIGGER trg_eoin_players_update_timestamp
        BEFORE UPDATE ON public.eoin_players
        FOR EACH ROW
        EXECUTE FUNCTION fn_update_timestamp();
    END IF;
END
$$;

COMMENT ON TABLE public.eoin_players IS 'Stores player biographical and draft information from Eoin Moore''s dataset.';
COMMENT ON COLUMN public.eoin_players.person_id IS 'Unique identifier for the player (from personId).';
COMMENT ON COLUMN public.eoin_players.height_inches IS 'Player height in inches.';
COMMENT ON COLUMN public.eoin_players.body_weight_lbs IS 'Player body weight in pounds.';
