#!/usr/bin/env python3
# Expected location: c:\Users\<USER>\Projects\nbadb\Database\etl\etl_orchestrator.py

import configparser
from datetime import datetime, timezone
import logging
import os
from pathlib import Path
import sqlite3
import sys
import time

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd

from database.db_manager import DatabaseManager
from etl.extractors import WyattKaggleCSVExtractor
from etl.loaders import PostgreSQLLoader
from etl.transformers import WyattFranchiseTeamTransformer

# Import the DatabaseManager


# Obtain a module-level logger
logger = logging.getLogger(__name__)

# Ensure the project root is in the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ETLOrchestrator:
    """
    Main ETL orchestration class that coordinates the extraction, transformation,
    and loading of NBA data from various sources into the PostgreSQL database.
    """
    
    def __init__(self, config_path='config.ini'):
        """
        Initialize the ETL orchestrator.
        
        Args:
            config_path (str): Path to the configuration file.
        """
        self.config_base_dir = os.path.dirname(os.path.abspath(config_path)) # Store base dir of config
        self.config = self._load_config(config_path)
        self.db_manager = DatabaseManager()
        self.chunk_size = 50000 # Default chunk size for reading large CSVs
        
        # Resolve and update data_directory in the config object itself for subsequent interpolations
        data_dir_raw = self.config.get('General', 'data_directory', fallback='data/') # fallback if not present
        resolved_data_dir = data_dir_raw 
        if not os.path.isabs(data_dir_raw):
            resolved_data_dir = os.path.join(self.config_base_dir, data_dir_raw)
        self.config['General']['data_directory'] = resolved_data_dir # Update for interpolations

        # Create data directories if they don't exist, using the resolved path
        os.makedirs(resolved_data_dir, exist_ok=True) # Use resolved_data_dir
        os.makedirs(os.path.join(resolved_data_dir, 'kaggle_datasets'), exist_ok=True)
        os.makedirs(os.path.join(resolved_data_dir, 'nba_api'), exist_ok=True)
        os.makedirs(os.path.join(resolved_data_dir, 'basketball_reference'), exist_ok=True)
        
        logger.info("ETL Orchestrator initialized successfully.")
    
    def _load_config(self, config_path):
        """
        Load configuration from the config file.
        
        Args:
            config_path (str): Path to the configuration file.
            
        Returns:
            configparser.ConfigParser: Loaded configuration.
        """
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        config = configparser.ConfigParser(interpolation=configparser.ExtendedInterpolation())
        config.read(config_path)
        logger.info(f"Configuration loaded from {config_path}")
        return config
    
    def _get_column_renames_from_config(self, file_name: str) -> dict:
        """Reads column renaming rules from the config file for a given file_name."""
        section_name = f"ColumnRenames_{file_name}"
        renames = {}
        if self.config.has_section(section_name):
            try:
                renames = dict(self.config.items(section_name))
                logger.debug(f"Loaded column renames for {file_name} from section '{section_name}': {renames}")
            except configparser.NoSectionError:
                logger.warning(f"Column renames section '{section_name}' not found in config for {file_name}.")
            except Exception as e:
                logger.error(f"Error reading column renames for {file_name} from section '{section_name}': {e}")
        else:
            logger.debug(f"No column renames section '{section_name}' found in config for {file_name}. No renames will be applied from config for this file.")
        return renames

    def run_etl_pipeline(self, sources=None, tables=None):
        """
        Run the complete ETL pipeline or specific parts of it.
        
        Args:
            sources (list, optional): List of data sources to process. If None, process all sources.
                Options: 'kaggle_eoin_moore', 'kaggle_wyatt_owalsh', 'nba_api', 'basketball_reference'
            tables (list, optional): List of specific tables to process. If None, process all tables.
        
        Returns:
            bool: True if the ETL process completed successfully, False otherwise.
        """
        start_time = time.time()
        logger.info(f"Starting ETL pipeline at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Default to all sources if none specified
        if sources is None:
            sources = ['kaggle_eoin_moore', 'kaggle_wyatt_owalsh', 'nba_api', 'basketball_reference']
        
        success = True
        
        try:
            # Connect to the database
            self.db_manager.connect()

            # Initialize/update database schema using DDL scripts
            self._initialize_database_schema()
            
            # Process each source
            for source in sources:
                source_start_time = time.time()
                logger.info(f"Processing source: {source}")
                
                try:
                    if source == 'kaggle_eoin_moore':
                        self._process_kaggle_eoin_moore(tables)
                    elif source == 'kaggle_wyatt_owalsh':
                        self._process_kaggle_wyatt_owalsh(tables)
                    elif source == 'nba_api':
                        self._process_nba_api(tables)
                    elif source == 'basketball_reference':
                        self._process_basketball_reference(tables)
                    else:
                        logger.warning(f"Unknown source: {source}")
                        continue
                    
                    source_end_time = time.time()
                    logger.info(f"Completed processing source {source} in {source_end_time - source_start_time:.2f} seconds")
                
                except Exception as e:
                    logger.error(f"Error processing source {source}: {str(e)}", exc_info=True)
                    success = False
            
            end_time = time.time()
            logger.info(f"ETL pipeline completed in {end_time - start_time:.2f} seconds")
            return success
        
        except Exception as e:
            logger.error(f"Error in ETL pipeline: {str(e)}", exc_info=True)
            return False
        
        finally:
            # Disconnect from the database
            self.db_manager.disconnect()
    
    def _initialize_database_schema(self):
        """
        Initializes the database schema by running DDL scripts.
        """
        try:
            schema_dir_raw = self.config.get('General', 'schema_directory')
            if not schema_dir_raw:
                logger.warning("Schema directory not configured in config.ini. Skipping DDL script execution.")
                return
            
            # Clean the directory path by removing potential comments
            schema_dir = schema_dir_raw.split(';')[0].strip()

            # Resolve schema_dir relative to the config file's location if it's not absolute
            if not os.path.isabs(schema_dir):
                schema_dir = os.path.join(self.config_base_dir, schema_dir)
            
            logger.info(f"Initializing database schema from DDL scripts in: {schema_dir}")
            self.db_manager.execute_ddl_scripts(schema_dir)
            logger.info("Database schema initialization complete.")
        except configparser.NoSectionError:
            logger.warning("General section not found in config.ini. Cannot find schema_directory.")
        except configparser.NoOptionError:
            logger.warning("schema_directory not found in General section of config.ini.")
        except Exception as e:
            logger.error(f"Error initializing database schema: {str(e)}", exc_info=True)
            raise
    
    def _process_kaggle_eoin_moore(self, tables=None):
        """
        Process data from Eoin A Moore's Kaggle dataset.
        
        Args:
            tables (list, optional): List of specific tables to process. If None, process all tables.
        """
        logger.info("Processing Eoin A Moore's Kaggle dataset")
        
        # Define a mapping for Eoin Moore's files to their config keys and target table names
        eoin_moore_files_info = {
            'player_stats': {
                'table_name': self.config.get('KaggleEoinMooreTables', 'player_statistics_table', fallback='player_statistics'),
                'config_key_name': 'eoin_moore_player_statistics_csv',
                'transform_identifier': 'PlayerStatistics' # Used to identify which transformations to apply
            },
            'team_stats': {
                'table_name': self.config.get('KaggleEoinMooreTables', 'team_statistics_table', fallback='team_statistics'),
                'config_key_name': 'eoin_moore_team_statistics_csv',
                'transform_identifier': 'TeamStatistics'
            },
            'game_logs': {
                'table_name': self.config.get('KaggleEoinMooreTables', 'game_logs_table', fallback='game_logs'),
                'config_key_name': 'eoin_moore_game_logs_csv',
                'transform_identifier': 'Games'
            },
            'league_schedule': {
                'table_name': self.config.get('KaggleEoinMooreTables', 'league_schedule_table', fallback='eoin_league_schedule'),
                'config_key_name': 'eoin_moore_league_schedule_csv',
                'transform_identifier': 'LeagueSchedule'
            },
            'players': {
                'table_name': self.config.get('KaggleEoinMooreTables', 'players_table', fallback='eoin_players'),
                'config_key_name': 'eoin_moore_players_csv',
                'transform_identifier': 'Players'
            }
        }
        
        # Process each defined file
        for internal_key, file_info in eoin_moore_files_info.items():
            table_name = file_info['table_name']
            config_key_name = file_info['config_key_name']
            transform_identifier = file_info['transform_identifier']

            # Filter based on `tables` argument if provided
            if tables and table_name not in tables:
                logger.debug(f"Skipping Eoin Moore's {internal_key} data (table {table_name}) as it's not in the specified tables list.")
                continue
            
            try:
                # Ensure we are reading from the correct config section for Eoin Moore's files
                csv_path = os.path.join(self.config.get('General', 'data_directory'), self.config.get('KaggleEoinMoore', config_key_name))
            except KeyError:
                logger.error(f"Config key '{config_key_name}' not found in [KaggleEoinMoore] section of config for Eoin Moore's {internal_key}.")
                continue # Skip this file if config key is missing
            
            if not os.path.exists(csv_path):
                logger.warning(f"CSV file not found: {csv_path} (for Eoin Moore's {internal_key})")
                continue
            
            logger.info(f"Loading data from {csv_path} into table {table_name}")
            total_rows_loaded_for_csv = 0
            chunk_count = 0

            # Specific handling for LeagueSchedule to truncate before loading
            if transform_identifier == 'LeagueSchedule':
                try:
                    logger.info(f"Truncating table {table_name} before loading new data for LeagueSchedule.")
                    self.db_manager.truncate_table(table_name)
                    logger.info(f"Successfully truncated {table_name}.")
                except Exception as e:
                    logger.error(f"Failed to truncate table {table_name} for LeagueSchedule. Error: {e}")
                    pass # Or raise e
            # Add truncation for other Eoin Moore tables that are fully reloaded
            elif transform_identifier in ['PlayerStatistics', 'TeamStatistics', 'Games']:
                try:
                    logger.info(f"Truncating table {table_name} before loading new data for {transform_identifier}.")
                    self.db_manager.truncate_table(table_name)
                    logger.info(f"Successfully truncated {table_name}.")
                except Exception as e:
                    logger.error(f"Failed to truncate table {table_name} for {transform_identifier}. Error: {e}")
                    # If truncation fails, we might want to stop the load for this file
                    # For now, log and let it proceed; it will likely fail on constraints or load duplicates
                    pass # Or raise e

            try:
                logger.info(f"Starting to read and process CSV in chunks: {csv_path} for {transform_identifier}")
                chunk_iterator = pd.read_csv(csv_path, chunksize=self.chunk_size, low_memory=False)
                all_chunks_processed_flag = False
                for chunk_df in chunk_iterator:
                    all_chunks_processed_flag = False # Reset for each chunk, will be true only if loop finishes AFTER last chunk
                    logger.info(f"Processing chunk {chunk_count+1} for {transform_identifier} from {csv_path} with {len(chunk_df)} rows.")
                    # Transform data for the current chunk
                    transformed_df_chunk = self._transform_eoin_moore_data(chunk_df, transform_identifier)
                    
                    logger.info(f"Attempting to load chunk {chunk_count+1} ({len(transformed_df_chunk)} rows) into {table_name}...")
                    # Load data into database for the current chunk
                    # The db_manager.load_df_to_table handles its own chunking for DB writes if needed.
                    rows_loaded_for_chunk = self.db_manager.load_df_to_table(
                        transformed_df_chunk, 
                        table_name, 
                        if_exists='append'
                    )
                    total_rows_loaded_for_csv += rows_loaded_for_chunk
                    logger.info(f"Successfully loaded chunk {chunk_count+1}. Rows in chunk: {rows_loaded_for_chunk}. Total rows loaded so far for {transform_identifier}: {total_rows_loaded_for_csv}")
                    chunk_count += 1
                    all_chunks_processed_flag = True # Mark true after successfully processing a chunk
                
                if all_chunks_processed_flag:
                    logger.info(f"Finished reading and processing all chunks from CSV: {csv_path} for {transform_identifier}")
                elif chunk_count > 0: # Some chunks were processed, but not all
                    logger.warning(f"CSV iteration for {csv_path} ({transform_identifier}) appears to have stopped prematurely after processing {chunk_count} chunks and loading {total_rows_loaded_for_csv} rows.")
                else: # No chunks were processed at all, pd.read_csv might have failed to even start iterating (e.g. file not found, empty file if not caught before)
                    logger.warning(f"CSV iteration for {csv_path} ({transform_identifier}) did not process any chunks. Please check the file and pandas compatibility.")

                logger.info(f"Successfully loaded a total of {total_rows_loaded_for_csv} rows from {transform_identifier} CSV into {table_name}")
            
            except Exception as e:
                logger.error(f"Error processing Eoin Moore's {transform_identifier} data: {str(e)}", exc_info=True)
                raise # Re-raise the exception to propagate the error
    
    def _transform_eoin_moore_data(self, df: pd.DataFrame, file_name: str) -> pd.DataFrame:
        logger.info(f"Transforming data for {file_name} using _transform_eoin_moore_data")
        
        logger.debug(f"[{file_name}] Columns before standardization: {df.columns.tolist()}")
        # 1. Standardize column names (lowercase, strip spaces, replace internal spaces with underscores)
        original_columns = df.columns.tolist()
        df.columns = [col.strip().lower().replace(' ', '_') for col in df.columns]
        standardized_columns = df.columns.tolist()
        logger.debug(f"[{file_name}] Columns after standardization: {standardized_columns}")
        if original_columns != standardized_columns:
            logger.info(f"[{file_name}] Standardized columns. Original: {original_columns}, New: {standardized_columns}")

        # 2. Apply configured column renames (from standardized names to final desired names)
        config_section_name = f"ColumnRenames_{file_name}"
        logger.debug(f"[{file_name}] Attempting to load renames from config section: {config_section_name}")
        column_renames = self._get_column_renames_from_config(file_name) # Use the new helper method
        logger.debug(f"[{file_name}] Fetched column_renames from config: {column_renames}")
        
        if column_renames:
            df.rename(columns=column_renames, inplace=True)
            logger.info(f"[{file_name}] Applied column renames from config. Columns after rename: {df.columns.tolist()}")
        else:
            logger.info(f"[{file_name}] No column renames configured or found for {file_name} in config.")

        # 3. File-specific transformations
        if file_name == 'PlayerStatistics.csv':
            logger.debug(f"Applying specific transformations for PlayerStatistics.csv")
            # TODO: Ensure existing transformations for PlayerStatistics.csv are maintained here.
            # Example: if 'player' in df.columns: df.rename(columns={'player': 'player_name'}, inplace=True)
            pass 

        elif file_name == 'TeamStatistics.csv':
            logger.debug(f"Applying specific transformations for TeamStatistics.csv")
            # TODO: Ensure existing transformations for TeamStatistics.csv are maintained here.
            pass

        elif file_name == 'Games.csv':
            logger.debug(f"Applying specific transformations for Games.csv")
            # TODO: Ensure existing transformations for Games.csv are maintained here.
            pass

        elif file_name == 'LeagueSchedule24_25.csv': 
            logger.debug(f"Applying specific transformations for LeagueSchedule24_25.csv")
            # TODO: Ensure existing transformations for LeagueSchedule24_25.csv are maintained here.
            # Example: Date/time parsing for 'game_datetime_est'
            if 'game_datetime_est' in df.columns:
                df['game_datetime_est'] = pd.to_datetime(df['game_datetime_est'], errors='coerce')
            # Example: Numeric conversion for IDs
            for col in ['game_id', 'home_team_id', 'away_team_id', 'season_year']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype('Int64')
            pass

        elif file_name == 'Players':
            logger.info(f"Applying detailed transformations for Players.csv. Current columns: {df.columns.tolist()}")
            
            PG_INTEGER_MIN = -2147483648
            PG_INTEGER_MAX = 2147483647

            integer_cols_final_names = ['height_inches', 'body_weight_lbs', 'draft_year', 'draft_round', 'draft_number']
            for col_name in integer_cols_final_names:
                if col_name in df.columns:
                    logger.debug(f"Processing integer column '{col_name}' for {file_name}")
                    numeric_series = pd.to_numeric(df[col_name], errors='coerce')
                    
                    if numeric_series.notna().any():
                        min_val, max_val = numeric_series.min(), numeric_series.max()
                        logger.debug(f"Column '{col_name}': Min/Max after pd.to_numeric: {min_val}/{max_val}")

                        condition = (numeric_series.notna()) & ((numeric_series < PG_INTEGER_MIN) | (numeric_series > PG_INTEGER_MAX))
                        out_of_range_indices = numeric_series[condition].index
                        
                        if not out_of_range_indices.empty:
                            num_out_of_range = len(out_of_range_indices)
                            logger.warning(f"Column '{col_name}': Found {num_out_of_range} values outside PostgreSQL INTEGER range ({PG_INTEGER_MIN} to {PG_INTEGER_MAX}). These will be set to NULL.")
                            problematic_original_values = df.loc[out_of_range_indices, col_name].head().tolist()
                            logger.warning(f"Problematic original values for '{col_name}' (first 5): {problematic_original_values}")
                            numeric_series.loc[out_of_range_indices] = pd.NA
                    
                    df[col_name] = numeric_series.round().astype('Int64')
                    logger.debug(f"Column '{col_name}' transformed. NA count: {df[col_name].isna().sum()}. Dtype: {df[col_name].dtype}")
                else:
                    logger.warning(f"Expected integer column '{col_name}' not found in DataFrame for {file_name}. Available: {df.columns.tolist()}")

            boolean_cols_final_names = ['is_guard', 'is_forward', 'is_center']
            for col in boolean_cols_final_names:
                if col in df.columns:
                    logger.debug(f"Processing boolean column '{col}' for {file_name}")
                    df[col] = df[col].astype(str).str.strip().str.lower().map({
                        'true': True, 'false': False,
                        '1': True, '0': False, '1.0': True, '0.0': False,
                        't': True, 'f': False,
                        'yes': True, 'no': False,
                        'nan': pd.NA, 'none': pd.NA, '<na>': pd.NA, '': pd.NA, 'nan.0': pd.NA
                    }).astype('boolean')
                    logger.debug(f"Column '{col}' transformed. NA count: {df[col].isna().sum()}. Dtype: {df[col].dtype}")
                else:
                    logger.warning(f"Expected boolean column '{col}' not found for {file_name}. Available: {df.columns.tolist()}")
            
            date_cols = ['birthdate']
            for col in date_cols:
                if col in df.columns:
                    logger.debug(f"Processing date column '{col}' for {file_name}")
                    df[col] = pd.to_datetime(df[col], errors='coerce').dt.date
                    logger.debug(f"Column '{col}' transformed to date. NA count: {df[col].isna().sum()}. Example: {df[col].dropna().head(1).tolist()}")

            if 'person_id' in df.columns:
                logger.debug(f"Processing 'person_id' (PK) column for {file_name}")
                df['person_id'] = pd.to_numeric(df['person_id'], errors='coerce')
                if df['person_id'].isna().any():
                    num_na_pk = df['person_id'].isna().sum()
                    logger.error(f"'person_id' in {file_name} has {num_na_pk} NA values after coercion. PK cannot be NULL. These rows will fail load if not handled.")
                df['person_id'] = df['person_id'].astype('Int64')
                logger.debug(f"Column 'person_id' transformed. NA count: {df['person_id'].isna().sum()}. Dtype: {df['person_id'].dtype}")
            else:
                logger.error(f"Critical: 'person_id' column not found in {file_name} after all renames. Columns: {df.columns.tolist()}")
        
        else:
            logger.info(f"No specific transformations applied for {file_name} in _transform_eoin_moore_data. DataFrame returned after generic steps.")

        logger.info(f"Finished transforming data for {file_name}. Resulting columns: {df.columns.tolist()}")
        return df
    
    def _process_kaggle_wyatt_owalsh(self, tables=None):
        """
        Process data from Wyatt O'Walsh's Kaggle dataset.
        Focuses on franchises and teams for now.
        
        Args:
            tables (list, optional): List of specific tables to process. 
                                     If 'franchises' or 'teams' is in the list, or if tables is None,
                                     this process will run.
        """
        if tables is not None and not any(t in tables for t in ['franchises', 'teams']):
            logger.info("Skipping Wyatt O'Walsh franchise/team processing as it's not in the specified tables list.")
            return

        logger.info("Processing Wyatt O'Walsh's Kaggle dataset for franchises and teams")

        try:
            # 1. Extract
            wyatt_extractor = WyattKaggleCSVExtractor(config=self.config)
            # Specify which data types to extract if needed, otherwise it defaults
            extracted_data = wyatt_extractor.extract(data_types=['team_history', 'teams_current_snapshot'])

            if not extracted_data or 'wyatt_team_history' not in extracted_data or 'wyatt_teams_current_snapshot' not in extracted_data:
                logger.error("Failed to extract necessary data from Wyatt O'Walsh's CSVs. Aborting franchise/team processing.")
                return

            # 2. Transform
            # The transformer needs the db_manager for lookups
            wyatt_transformer = WyattFranchiseTeamTransformer(db_manager=self.db_manager)
            transformed_data = wyatt_transformer.transform(extracted_data)

            if not transformed_data or 'franchises_df' not in transformed_data or 'teams_df' not in transformed_data:
                logger.error("Failed to transform Wyatt O'Walsh's team/franchise data. Aborting loading.")
                return

            franchises_to_load = transformed_data['franchises_df']
            teams_to_load = transformed_data['teams_df']

            # 3. Load
            pg_loader = PostgreSQLLoader(db_manager=self.db_manager)
            load_results = pg_loader.load_franchises_and_teams(
                franchises_df=franchises_to_load,
                teams_df=teams_to_load
            )

            logger.info(f"Wyatt O'Walsh franchise/team loading complete. Results: {load_results}")

        except Exception as e:
            logger.error(f"Error processing Wyatt O'Walsh's franchise/team data: {str(e)}", exc_info=True)
            # Decide if this should re-raise or just log and continue with other sources/tables
            # For now, let's log and let the main orchestrator decide if it's a fatal error for the whole pipeline

    def _process_nba_api(self, tables=None):
        """
        Process data from the NBA API.
        
        Args:
            tables (list, optional): List of specific tables to process. If None, process all tables.
        """
        logger.info("Processing NBA API data")
        
        try:
            from nba_api.stats.endpoints import commonallplayers, commonteamroster, leaguegamefinder
            
            # Define API endpoints to process
            api_endpoints = {
                'players': self._fetch_nba_api_players,
                'teams': self._fetch_nba_api_teams,
                'games': self._fetch_nba_api_games
            }
            
            # Filter endpoints if tables specified
            if tables:
                api_endpoints = {k: v for k, v in api_endpoints.items() if k in tables}
            
            # Process each endpoint
            for endpoint_name, fetch_function in api_endpoints.items():
                logger.info(f"Fetching data from NBA API endpoint: {endpoint_name}")
                
                # Fetch and transform data
                df = fetch_function()
                
                # Map endpoint to PostgreSQL table
                pg_table_name = f"nba_api_{endpoint_name}"
                
                # Load data into PostgreSQL
                if df is not None and not df.empty:
                    self.db_manager.load_df_to_table(df, pg_table_name, if_exists='append')
                    logger.info(f"Successfully loaded {len(df)} rows from NBA API {endpoint_name} into {pg_table_name}")
                else:
                    logger.warning(f"No data fetched from NBA API endpoint: {endpoint_name}")
        
        except ImportError:
            logger.error("NBA API package not installed. Install with: pip install nba-api")
            raise
        except Exception as e:
            logger.error(f"Error processing NBA API data: {str(e)}", exc_info=True)
            raise
    
    def _fetch_nba_api_players(self):
        """
        Fetch player data from NBA API.
        
        Returns:
            pandas.DataFrame: DataFrame containing player data.
        """
        from nba_api.stats.endpoints import commonallplayers
        
        # Get all players
        players = commonallplayers.CommonAllPlayers()
        df = players.get_data_frames()[0]
        
        # Apply transformations
        # (Add specific transformations here)
        
        return df
    
    def _fetch_nba_api_teams(self):
        """
        Fetch team data from NBA API.
        
        Returns:
            pandas.DataFrame: DataFrame containing team data.
        """
        from nba_api.stats.endpoints import leaguedashteamstats
        
        # Get team stats
        teams = leaguedashteamstats.LeagueDashTeamStats()
        df = teams.get_data_frames()[0]
        
        # Apply transformations
        # (Add specific transformations here)
        
        return df
    
    def _fetch_nba_api_games(self):
        """
        Fetch game data from NBA API.
        
        Returns:
            pandas.DataFrame: DataFrame containing game data.
        """
        from nba_api.stats.endpoints import leaguegamefinder
        
        # Get game data
        games = leaguegamefinder.LeagueGameFinder()
        df = games.get_data_frames()[0]
        
        # Apply transformations
        # (Add specific transformations here)
        
        return df
    
    def _process_basketball_reference(self, tables=None):
        """
        Process data from Basketball Reference.
        
        Args:
            tables (list, optional): List of specific tables to process. If None, process all tables.
        """
        logger.info("Processing Basketball Reference data")
        
        try:
            from basketball_reference_web_scraper import client
            
            # Define data types to fetch
            data_types = {
                'players': self._fetch_bball_ref_players,
                'teams': self._fetch_bball_ref_teams,
                'games': self._fetch_bball_ref_games
            }
            
            # Filter data types if tables specified
            if tables:
                data_types = {k: v for k, v in data_types.items() if k in tables}
            
            # Process each data type
            for data_type, fetch_function in data_types.items():
                logger.info(f"Fetching {data_type} data from Basketball Reference")
                
                # Fetch and transform data
                df = fetch_function()
                
                # Map data type to PostgreSQL table
                pg_table_name = f"bball_ref_{data_type}"
                
                # Load data into PostgreSQL
                if df is not None and not df.empty:
                    self.db_manager.load_df_to_table(df, pg_table_name, if_exists='append')
                    logger.info(f"Successfully loaded {len(df)} rows of {data_type} from Basketball Reference into {pg_table_name}")
                else:
                    logger.warning(f"No {data_type} data fetched from Basketball Reference")
        
        except ImportError:
            logger.error("Basketball Reference scraper not installed. Install with: pip install basketball-reference-web-scraper")
            raise
        except Exception as e:
            logger.error(f"Error processing Basketball Reference data: {str(e)}", exc_info=True)
            raise
    
    def _fetch_bball_ref_players(self):
        """
        Fetch player data from Basketball Reference.
        
        Returns:
            pandas.DataFrame: DataFrame containing player data.
        """
        from basketball_reference_web_scraper import client
        
        # Get players for the current season (adjust as needed)
        players = client.players_season_totals(season_end_year=2023)
        df = pd.DataFrame(players)
        
        # Apply transformations
        # (Add specific transformations here)
        
        return df
    
    def _fetch_bball_ref_teams(self):
        """
        Fetch team data from Basketball Reference.
        
        Returns:
            pandas.DataFrame: DataFrame containing team data.
        """
        from basketball_reference_web_scraper import client
        
        # Get team box scores for a sample of games (adjust as needed)
        # This is just an example - you may need to aggregate data from multiple sources
        team_games = client.team_box_scores(day=1, month=1, year=2023)
        df = pd.DataFrame(team_games)
        
        # Extract unique teams
        if not df.empty and 'team' in df.columns:
            df = df[['team']].drop_duplicates()
        
        # Apply transformations
        # (Add specific transformations here)
        
        return df
    
    def _fetch_bball_ref_games(self):
        """
        Fetch game data from Basketball Reference for a specified season and optionally filter by date.
        
        Returns:
            pandas.DataFrame: DataFrame containing game data.
        """
        from basketball_reference_web_scraper import client
        import pandas as pd
        import time
        from datetime import datetime, timezone

        # Define the season to fetch. Example: 2023 for the 2022-2023 season.
        # This could be made configurable later.
        season_end_year_to_fetch = 2023
        
        # Define the target date range (e.g., first week of January 2023)
        # This also could be made configurable or removed if not needed.
        target_start_date = datetime(season_end_year_to_fetch, 1, 1, tzinfo=timezone.utc)
        target_end_date = datetime(season_end_year_to_fetch, 1, 7, tzinfo=timezone.utc)
        df = pd.DataFrame() # Initialize df to ensure it's always defined

        # Convert target dates to Pandas Timestamps
        pd_target_start_date = pd.Timestamp(target_start_date)
        pd_target_end_date = pd.Timestamp(target_end_date)

        try:
            logger.info(f"Fetching season schedule for season ending in {season_end_year_to_fetch} from Basketball Reference.")
            # Fetch all games for the season
            season_games_data = client.season_schedule(season_end_year=season_end_year_to_fetch)
            
            # Add a delay to be polite to the server after the main fetch
            delay_str = self.config.get('BasketballReference', 'delay_seconds', fallback='1')
            try:
                delay = int(delay_str)
            except ValueError:
                logger.warning(f"Invalid delay_seconds '{delay_str}', defaulting to 1 second.")
                delay = 1
            time.sleep(delay)

            if not season_games_data:
                logger.warning(f"No game data returned for season ending in {season_end_year_to_fetch}.")
                # df is already an empty DataFrame, so no action needed here
            else:
                current_df = pd.DataFrame(season_games_data) # Use a temporary df

                # Ensure 'start_time' column exists and convert to datetime for filtering
                if 'start_time' in current_df.columns:
                    current_df['start_time'] = pd.to_datetime(current_df['start_time'], errors='coerce')
                    
                    # Filter for the target date range using Pandas Timestamps
                    df_filtered = current_df[(current_df['start_time'] >= pd_target_start_date) & (current_df['start_time'] <= pd_target_end_date)].copy()
                    logger.info(f"Filtered {len(df_filtered)} games for the period {target_start_date.date()} to {target_end_date.date()} from a total of {len(current_df)} season games.")
                    df = df_filtered # Assign to the main df
                else:
                    logger.warning("'start_time' column not found in fetched game data. Cannot filter by date. Returning all games for the season.")
                    df = current_df # Assign unfiltered data to df

        except Exception as e:
            logger.error(f"Error fetching or processing games for season ending in {season_end_year_to_fetch}: {str(e)}", exc_info=True)
            # df is already an empty DataFrame, so no action needed here
        
        # Apply transformations
        # (Add specific transformations here)
        
        return df


if __name__ == "__main__":
    try:
        # Parse command line arguments
        import argparse
        
        parser = argparse.ArgumentParser(description='NBA Database ETL Orchestrator')
        parser.add_argument('--config', default='config.ini', help='Path to configuration file')
        parser.add_argument('--sources', nargs='*', help='Data sources to process (e.g., kaggle_eoin_moore kaggle_wyatt_owalsh nba_api basketball_reference)')
        parser.add_argument('--tables', nargs='*', help='Specific tables to process')
        
        args = parser.parse_args()
        
        # Initialize and run ETL orchestrator
        etl = ETLOrchestrator(config_path=args.config)
        success = etl.run_etl_pipeline(sources=args.sources, tables=args.tables)

        if success:
            logger.info("ETL pipeline completed successfully overall.")
        else:
            logger.error("ETL pipeline encountered errors.")
            sys.exit(1) # Exit with error code if pipeline failed
            
    except FileNotFoundError as e:
        logger.error(f"Configuration file error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"An unexpected error occurred in the ETL orchestrator: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logging.shutdown() # Ensure all logging handlers are closed and flushed
