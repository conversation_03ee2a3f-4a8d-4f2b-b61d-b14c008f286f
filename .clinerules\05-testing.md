# Testing Standards

## Test Requirements
- **Unit Tests:** Must be provided for new or modified business logic, complex algorithms, and utility functions. Aim for good coverage of logical paths and edge cases.
- **Integration Tests:** Consider and suggest where integration tests are needed, especially for API interactions or database operations. (<PERSON><PERSON> may not write these itself but should plan for them).
- **E2E Tests:** Mention critical user flows that should be covered by E2E tests.

## Testable Code
- Design code with testability in mind (SRP, DI, Interfaces). Point out if proposed code is difficult to test and suggest improvements.
- Write clear, descriptive test names that explain the test's purpose.
- Tests should be independent and repeatable.
- Suggest appropriate mocking/stubbing strategies for dependencies in unit tests.