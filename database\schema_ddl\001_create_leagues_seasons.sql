-- 001_create_leagues_seasons.sql
-- DDL for Leagues and Seasons tables

-- Leagues Table: Stores information about different basketball leagues
CREATE TABLE IF NOT EXISTS Leagues (
    league_id SERIAL PRIMARY KEY,
    league_name VARCHAR(100) UNIQUE NOT NULL,
    league_abbreviation VARCHAR(10) UNIQUE NOT NULL,
    founded_year INTEGER,
    disbanded_year INTEGER,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE Leagues IS 'Stores information about different basketball leagues (e.g., NBA, ABA, BAA, NBL).';
COMMENT ON COLUMN Leagues.league_id IS 'Unique identifier for the league.';
COMMENT ON COLUMN Leagues.league_name IS 'Full name of the league (e.g., "National Basketball Association").';
COMMENT ON COLUMN Leagues.league_abbreviation IS 'Common abbreviation (e.g., "NBA", "ABA").';
COMMENT ON COLUMN Leagues.founded_year IS 'Year the league was founded.';
COMMENT ON COLUMN Leagues.disbanded_year IS 'Year the league was disbanded, if applicable.';
COMMENT ON COLUMN Leagues.notes IS 'Any relevant notes about the league.';
COMMENT ON COLUMN Leagues.created_at IS 'Timestamp of when the record was created.';
COMMENT ON COLUMN Leagues.updated_at IS 'Timestamp of when the record was last updated.';


-- Seasons Table: Stores information about individual seasons within each league
CREATE TABLE IF NOT EXISTS Seasons (
    season_id SERIAL PRIMARY KEY,
    league_id INTEGER NOT NULL,
    year_start INTEGER NOT NULL,
    year_end INTEGER NOT NULL,
    season_display_name VARCHAR(20) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_league
        FOREIGN KEY(league_id)
        REFERENCES Leagues(league_id)
        ON DELETE CASCADE, -- If a league is deleted, its seasons are also deleted
    CONSTRAINT uq_season_league_year
        UNIQUE (league_id, year_start),
    CONSTRAINT chk_season_year_order
        CHECK (year_end >= year_start)
);

COMMENT ON TABLE Seasons IS 'Stores information about individual seasons within each league.';
COMMENT ON COLUMN Seasons.season_id IS 'Unique identifier for the season.';
COMMENT ON COLUMN Seasons.league_id IS 'Foreign key referencing the league this season belongs to.';
COMMENT ON COLUMN Seasons.year_start IS 'The calendar year the season started (e.g., 2022 for the 2022-23 season).';
COMMENT ON COLUMN Seasons.year_end IS 'The calendar year the season ended (e.g., 2023 for the 2022-23 season).';
COMMENT ON COLUMN Seasons.season_display_name IS 'A human-readable string for the season (e.g., "2022-23", "1946-47").';
COMMENT ON COLUMN Seasons.notes IS 'Any relevant notes about the season (e.g., lockout-shortened, rule changes introduced).';
COMMENT ON COLUMN Seasons.created_at IS 'Timestamp of when the record was created.';
COMMENT ON COLUMN Seasons.updated_at IS 'Timestamp of when the record was last updated.';

-- Trigger function to update 'updated_at' timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for Leagues table
CREATE OR REPLACE TRIGGER set_leagues_timestamp
BEFORE UPDATE ON Leagues
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Triggers for Seasons table
CREATE OR REPLACE TRIGGER set_seasons_timestamp
BEFORE UPDATE ON Seasons
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Seed data for common leagues
INSERT INTO Leagues (league_name, league_abbreviation, founded_year) 
VALUES ('National Basketball Association', 'NBA', 1946)
ON CONFLICT (league_abbreviation) DO NOTHING;
