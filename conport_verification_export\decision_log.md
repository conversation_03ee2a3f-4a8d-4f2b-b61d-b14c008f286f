# Decision Log

---
## Decision
*   [2025-05-29 18:50:06] Windows-Compatible MCP Server Configuration

## Rationale
*   MCP server setup on Windows requires specific path handling and command configuration to ensure reliable server launching and communication.

## Implementation Details
*   Full path to npx-cli.js with node command: C:\Program Files\nodejs\node_modules\npm\bin\npx-cli.js. Configuration in cline_mcp_settings.json with stdio type and specific args array for PostgreSQL server.

---
## Decision
*   [2025-05-29 18:50:06] Chunking Strategy for Large Dataset Processing

## Rationale
*   Processing large CSV files (1.6M+ rows) requires memory-efficient approaches to prevent system resource exhaustion and enable reliable data loading into PostgreSQL.

## Implementation Details
*   Chunking implemented in data loading processes. PlayerStatistics successfully processed 1,627,146 rows using chunked loading approach. Critical for handling large datasets from Kaggle sources.

---
## Decision
*   [2025-05-29 18:50:06] Transform Identifier Matching Pattern

## Rationale
*   Ensuring transformation logic is correctly triggered requires precise matching between file identifiers and conditional logic. Prevents silent failures where transformations are defined but not applied.

## Implementation Details
*   Changed elif file_name == 'Players.csv': to elif file_name == 'Players': in _transform_eoin_moore_data method. Pattern ensures file name matching aligns with processing logic throughout ETL pipeline.

---
## Decision
*   [2025-05-29 18:50:06] Idempotency Strategy for Data Loading

## Rationale
*   ETL processes must be repeatable and safe to re-run without causing data corruption or duplication. Idempotency ensures data integrity and enables reliable pipeline execution in production environments.

## Implementation Details
*   ON CONFLICT clauses for upserts, table truncation for full reloads. load_df_to_table method in db_manager.py handles pandas DataFrame insertion with chunking for large datasets. DDL scripts executed in numerical order.

---
## Decision
*   [2025-05-29 18:50:05] Centralized Logging Implementation

## Rationale
*   Comprehensive logging using Python's logging module provides essential visibility into ETL operations, errors, and data flow. Critical for troubleshooting complex data processing pipelines and monitoring system health.

## Implementation Details
*   Logs stored in logs/ directory with timestamped files (etl_TIMESTAMP.log). Debug logging enabled for detailed troubleshooting. Real-time monitoring via logs/etl.log. All ETL components use centralized logging framework.

---
## Decision
*   [2025-05-29 18:38:09] Source Precedence Hierarchy for Conflict Resolution

## Rationale
*   Establishing a clear hierarchy (NBA.com API > Basketball-Reference.com > Kaggle > Custom Scrapers) ensures consistent data quality and provides a systematic approach to resolving conflicts when multiple sources provide overlapping data.

## Implementation Details
*   Design goal established, though current implementation focuses on distinct data streams. Future conflict resolution will follow this precedence order.

---
## Decision
*   [2025-05-29 18:38:09] Configuration-Driven ETL Architecture

## Rationale
*   Externalizing configuration details from code improves maintainability, allows for environment-specific settings, and enables dynamic column mapping without code changes. Critical for handling diverse data source formats.

## Implementation Details
*   config.ini manages ETL parameters, column renames, API delays. .env handles sensitive credentials. Pattern proven successful for PlayerStatistics, TeamStatistics, Games, and LeagueSchedule processing.

---
## Decision
*   [2025-05-29 18:38:09] Hybrid Data Ingestion Strategy

## Rationale
*   A multi-source approach combining bulk loading from local datasets (Kaggle), systematic API consumption (nba_api, basketball_reference_web_scraper), and planned custom web scraping provides comprehensive data coverage while respecting rate limits and data availability.

## Implementation Details
*   ETL pipeline supports multiple extractors: KaggleCSVExtractor, NBAAPIExtractor, WyattKaggleCSVExtractor, with planned custom scrapers for additional sources

---
## Decision
*   [2025-05-29 18:38:09] Python 3.9+ as Primary Language

## Rationale
*   Python was selected due to its strong data manipulation libraries (Pandas, NumPy) and rich ecosystem for web scraping and API interaction. The language provides excellent support for ETL operations and database connectivity.

## Implementation Details
*   Python 3.10.11 confirmed and active via virtual environment (venv). All ETL components built in Python with comprehensive library support.

---
## Decision
*   [2025-05-29 18:38:09] PostgreSQL Database Choice

## Rationale
*   PostgreSQL was chosen for its robustness, SQL compliance, and features suitable for relational data. It provides the reliability and performance needed for a comprehensive basketball data warehouse.

## Implementation Details
*   Local PostgreSQL 17 instance setup with dedicated database (nba_data_db) and user credentials managed via .env file

---
## Decision
*   [2025-05-29 14:12:50] Testing ConPort MCP server functionality

## Rationale
*   ConPort provides a comprehensive knowledge management system that can track decisions, progress, patterns, and custom data across the project lifecycle. Testing its features will help determine how to best integrate it into the NBA database project workflow.

## Implementation Details
*   Tested key ConPort features including context retrieval, progress tracking, semantic search, and data logging. Found that semantic search works well for finding related issues like column mapping problems in the ETL pipeline.
