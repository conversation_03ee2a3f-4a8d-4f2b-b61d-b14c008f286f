#!/usr/bin/env python3
# Expected location: c:\Users\<USER>\Projects\nbadb\Database\etl\transformers.py

import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Optional, Dict, List, Any, Tuple

# Configure logging
logger = logging.getLogger('etl_transformers')

class BaseTransformer:
    """Base class for all data transformers."""
    
    def __init__(self, db_schema=None):
        """
        Initialize the transformer.
        
        Args:
            db_schema (dict, optional): Database schema information for validation.
        """
        self.db_schema = db_schema
    
    def transform(self, data, source_name, table_name):
        """
        Transform data from source format to database format.
        
        Args:
            data (pandas.DataFrame): Source data.
            source_name (str): Name of the data source.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Transformed data.
        """
        raise NotImplementedError("Subclasses must implement transform()")


class KaggleTransformer(BaseTransformer):
    """Transformer for Kaggle datasets."""
    
    def transform(self, data, source_name, table_name):
        """
        Transform Kaggle data to match database schema.
        
        Args:
            data (pandas.DataFrame): Source data.
            source_name (str): Name of the Kaggle dataset.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Transformed data.
        """
        if data.empty:
            return data
        
        # Make a copy to avoid modifying the original
        df = data.copy()
        
        # Apply common transformations
        df = self._clean_column_names(df)
        df = self._handle_missing_values(df)
        
        # Apply specific transformations based on source and table
        if source_name == 'eoin_moore':
            df = self._transform_eoin_moore(df, table_name)
        elif source_name == 'wyatt_owalsh':
            df = self._transform_wyatt_owalsh(df, table_name)
        
        # Validate against schema if available
        if self.db_schema and table_name in self.db_schema:
            df = self._validate_against_schema(df, table_name)
        
        return df
    
    def _clean_column_names(self, df):
        """
        Clean column names to match database conventions.
        
        Args:
            df (pandas.DataFrame): DataFrame to clean.
            
        Returns:
            pandas.DataFrame: DataFrame with cleaned column names.
        """
        # Convert to lowercase and replace spaces with underscores
        df.columns = [col.lower().replace(' ', '_') for col in df.columns]
        
        # Remove special characters
        df.columns = [col.replace('(', '').replace(')', '').replace('%', 'pct').replace('+', 'plus') for col in df.columns]
        
        return df
    
    def _handle_missing_values(self, df):
        """
        Handle missing values based on column types.
        
        Args:
            df (pandas.DataFrame): DataFrame to process.
            
        Returns:
            pandas.DataFrame: DataFrame with handled missing values.
        """
        # For numeric columns, replace NaN with None (SQL NULL)
        for col in df.select_dtypes(include=['number']).columns:
            df[col] = df[col].apply(lambda x: None if pd.isna(x) else x)
        
        # For string columns, replace NaN with None
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].apply(lambda x: None if pd.isna(x) else str(x))
        
        return df
    
    def _transform_eoin_moore(self, df, table_name):
        """
        Apply transformations specific to Eoin Moore's dataset.
        
        Args:
            df (pandas.DataFrame): DataFrame to transform.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Transformed DataFrame.
        """
        # Apply specific transformations based on table
        if table_name == 'player_statistics':
            # Example transformations for player statistics
            if 'player' in df.columns:
                df.rename(columns={'player': 'player_name'}, inplace=True)
            
            # Convert percentage columns to decimal format if needed
            pct_columns = [col for col in df.columns if 'pct' in col or 'percentage' in col]
            for col in pct_columns:
                if df[col].dtype == 'float64' or df[col].dtype == 'float32':
                    # Check if values are already in decimal format (0-1)
                    if df[col].max() > 1:
                        df[col] = df[col] / 100
            
        elif table_name == 'team_statistics':
            # Example transformations for team statistics
            if 'team' in df.columns:
                df.rename(columns={'team': 'team_name'}, inplace=True)
            
            # Similar percentage conversions as above
            
        elif table_name == 'game_logs':
            # Example transformations for game logs
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
        
        return df
    
    def _transform_wyatt_owalsh(self, df, table_name):
        """
        Apply transformations specific to Wyatt O'Walsh's dataset.
        
        Args:
            df (pandas.DataFrame): DataFrame to transform.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Transformed DataFrame.
        """
        # Apply specific transformations based on table
        # This is a placeholder - actual transformations will depend on the specific data structure
        
        # Convert date columns to datetime
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        for col in date_columns:
            try:
                df[col] = pd.to_datetime(df[col])
            except:
                pass
        
        return df
    
    def _validate_against_schema(self, df, table_name):
        """
        Validate and adjust DataFrame to match database schema.
        
        Args:
            df (pandas.DataFrame): DataFrame to validate.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Validated DataFrame.
        """
        schema = self.db_schema[table_name]
        
        # Check for required columns
        for col in schema:
            if col not in df.columns:
                # Add missing column with None values
                df[col] = None
                logger.warning(f"Added missing column '{col}' to table '{table_name}'")
        
        # Remove extra columns
        extra_cols = [col for col in df.columns if col not in schema]
        if extra_cols:
            df = df.drop(columns=extra_cols)
            logger.warning(f"Removed extra columns {extra_cols} from table '{table_name}'")
        
        return df


class NBAAPITransformer(BaseTransformer):
    """Transformer for NBA API data."""
    
    def transform(self, data, endpoint, table_name):
        """
        Transform NBA API data to match database schema.
        
        Args:
            data (pandas.DataFrame): Source data.
            endpoint (str): Name of the API endpoint.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Transformed data.
        """
        if data.empty:
            return data
        
        # Make a copy to avoid modifying the original
        df = data.copy()
        
        # Clean column names
        df.columns = [col.lower() for col in df.columns]
        
        # Apply specific transformations based on endpoint
        if 'players' in endpoint:
            df = self._transform_players(df)
        elif 'teams' in endpoint:
            df = self._transform_teams(df)
        elif 'games' in endpoint:
            df = self._transform_games(df)
        
        # Validate against schema if available
        if self.db_schema and table_name in self.db_schema:
            df = self._validate_against_schema(df, table_name)
        
        return df
    
    def _transform_players(self, df):
        """Transform player data from NBA API."""
        # Example transformations for player data
        # Map NBA API column names to database column names
        column_mapping = {
            'person_id': 'player_id',
            'display_first_last': 'player_name',
            'team_id': 'team_id',
            'team_name': 'team_name',
            'team_abbreviation': 'team_abbreviation'
        }
        
        # Rename columns that exist in the DataFrame
        existing_cols = [col for col in column_mapping if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_cols})
        
        return df
    
    def _transform_teams(self, df):
        """Transform team data from NBA API."""
        # Example transformations for team data
        column_mapping = {
            'team_id': 'team_id',
            'team_name': 'team_name',
            'team_abbreviation': 'team_abbreviation',
            'team_city': 'city',
            'team_state': 'state_province',
            'team_conference': 'conference',
            'team_division': 'division'
        }
        
        existing_cols = [col for col in column_mapping if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_cols})
        
        return df
    
    def _transform_games(self, df):
        """Transform game data from NBA API."""
        # Example transformations for game data
        column_mapping = {
            'game_id': 'game_id',
            'game_date': 'game_date',
            'matchup': 'matchup',
            'wl': 'win_loss',
            'team_id': 'team_id',
            'team_name': 'team_name',
            'pts': 'points',
            'plus_minus': 'plus_minus'
        }
        
        existing_cols = [col for col in column_mapping if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_cols})
        
        # Convert date columns to datetime
        if 'game_date' in df.columns:
            df['game_date'] = pd.to_datetime(df['game_date'])
        
        return df
    
    def _validate_against_schema(self, df, table_name):
        """
        Validate and adjust DataFrame to match database schema.
        
        Args:
            df (pandas.DataFrame): DataFrame to validate.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Validated DataFrame.
        """
        schema = self.db_schema[table_name]
        
        # Check for required columns
        for col in schema:
            if col not in df.columns:
                # Add missing column with None values
                df[col] = None
                logger.warning(f"Added missing column '{col}' to table '{table_name}'")
        
        # Remove extra columns
        extra_cols = [col for col in df.columns if col not in schema]
        if extra_cols:
            df = df.drop(columns=extra_cols)
            logger.warning(f"Removed extra columns {extra_cols} from table '{table_name}'")
        
        return df


class BasketballReferenceTransformer(BaseTransformer):
    """Transformer for Basketball Reference data."""
    
    def transform(self, data, data_type, table_name):
        """
        Transform Basketball Reference data to match database schema.
        
        Args:
            data (pandas.DataFrame): Source data.
            data_type (str): Type of data (players, teams, games).
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Transformed data.
        """
        if data.empty:
            return data
        
        # Make a copy to avoid modifying the original
        df = data.copy()
        
        # Apply specific transformations based on data type
        if 'players' in data_type:
            df = self._transform_players(df)
        elif 'teams' in data_type:
            df = self._transform_teams(df)
        elif 'games' in data_type:
            df = self._transform_games(df)
        
        # Validate against schema if available
        if self.db_schema and table_name in self.db_schema:
            df = self._validate_against_schema(df, table_name)
        
        return df
    
    def _transform_players(self, df):
        """Transform player data from Basketball Reference."""
        # Example transformations for player data
        # Convert enum values to strings if needed
        if 'team' in df.columns and df['team'].dtype == 'object':
            df['team'] = df['team'].astype(str)
        
        # Rename columns to match database schema
        column_mapping = {
            'name': 'player_name',
            'team': 'team_name',
            'position': 'position',
            'age': 'age',
            'games_played': 'games_played',
            'minutes_played': 'minutes_played'
        }
        
        existing_cols = [col for col in column_mapping if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_cols})
        
        return df
    
    def _transform_teams(self, df):
        """Transform team data from Basketball Reference."""
        # Example transformations for team data
        column_mapping = {
            'team': 'team_name',
            'conference': 'conference',
            'division': 'division',
            'wins': 'wins',
            'losses': 'losses'
        }
        
        existing_cols = [col for col in column_mapping if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_cols})
        
        return df
    
    def _transform_games(self, df):
        """Transform game data from Basketball Reference."""
        # Example transformations for game data
        column_mapping = {
            'start_time': 'game_date',
            'home_team': 'home_team',
            'away_team': 'away_team',
            'home_team_score': 'home_score',
            'away_team_score': 'away_score'
        }
        
        existing_cols = [col for col in column_mapping if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_cols})
        
        # Convert date columns to datetime
        if 'game_date' in df.columns:
            df['game_date'] = pd.to_datetime(df['game_date'])
        
        return df
    
    def _validate_against_schema(self, df, table_name):
        """
        Validate and adjust DataFrame to match database schema.
        
        Args:
            df (pandas.DataFrame): DataFrame to validate.
            table_name (str): Name of the target table.
            
        Returns:
            pandas.DataFrame: Validated DataFrame.
        """
        schema = self.db_schema[table_name]
        
        # Check for required columns
        for col in schema:
            if col not in df.columns:
                # Add missing column with None values
                df[col] = None
                logger.warning(f"Added missing column '{col}' to table '{table_name}'")
        
        # Remove extra columns
        extra_cols = [col for col in df.columns if col not in schema]
        if extra_cols:
            df = df.drop(columns=extra_cols)
            logger.warning(f"Removed extra columns {extra_cols} from table '{table_name}'")
        
        return df


class WyattFranchiseTeamTransformer(BaseTransformer):
    """
    Transforms Wyatt O'Walsh's team history and current team snapshot data
    into normalized 'franchises' and 'teams' DataFrames.
    """

    def __init__(self, db_manager, db_schema=None):
        """
        Initialize the transformer.
        
        Args:
            db_manager (DBManager): Instance of DBManager for database lookups.
            db_schema (dict, optional): Database schema information for validation.
        """
        super().__init__(db_schema)
        self.db_manager = db_manager
        self._league_map = {} # Cache for league_id lookups
        self._season_id_cache = {} # Cache for season_id lookups

    def _get_league_id(self, league_abbreviation):
        """Helper to get league_id from abbreviation, caching results."""
        if not league_abbreviation: # Handle empty or None league_abbreviation
            return None
        if league_abbreviation not in self._league_map:
            query = "SELECT league_id FROM public.leagues WHERE league_abbreviation = %s"
            try:
                result = self.db_manager.execute_query(query, (league_abbreviation,), fetch_mode='one')
                self._league_map[league_abbreviation] = result[0] if result else None
            except Exception as e:
                logger.error(f"Error querying league_id for {league_abbreviation}: {e}")
                self._league_map[league_abbreviation] = None
        return self._league_map[league_abbreviation]

    def _ensure_season_exists(self, year_start: int, league_id: int) -> Optional[int]:
        """Ensures a season entry exists in the database, creating it if necessary."""
        year_end = year_start + 1
        season_display_name = f"{year_start}-{(year_start + 1) % 100:02d}"

        insert_query = """
        INSERT INTO Seasons (league_id, year_start, year_end, season_display_name)
        VALUES (%s, %s, %s, %s)
        ON CONFLICT (league_id, year_start) DO NOTHING;
        """
        select_query = "SELECT season_id FROM Seasons WHERE year_start = %s AND league_id = %s;"

        try:
            logger.info(f"Attempting to ensure season exists for year {year_start}, league_id {league_id}.")
            
            # Use cursor directly for DML and explicit commit
            self.db_manager.cursor.execute(insert_query, (league_id, year_start, year_end, season_display_name))
            self.db_manager.conn.commit()
            
            # After attempting insert (or if it conflicted), select the season_id
            result = self.db_manager.execute_query(select_query, (year_start, league_id), fetch_mode='one')
            
            if result:
                season_id = result[0]
                logger.info(f"Ensured season exists: year {year_start}, league_id {league_id}, season_id {season_id}.")
                self._season_id_cache[(league_id, year_start)] = season_id # Update cache
                return season_id
            else:
                logger.error(f"Failed to find season for year {year_start}, league_id {league_id} even after insert attempt.")
                return None
        except Exception as e:
            logger.error(f"Error in _ensure_season_exists for year {year_start}, league_id {league_id}: {e}")
            try:
                self.db_manager.conn.rollback() # Rollback on error
            except Exception as rb_e:
                logger.error(f"Error during rollback: {rb_e}")
            return None

    def _get_season_id(self, year_start: int, league_id: int) -> Optional[int]:
        """Retrieves season_id from cache or database. Creates season if not found."""
        if not isinstance(year_start, int):
            try:
                year_start = int(year_start)
            except ValueError:
                logger.error(f"Invalid year_start value for season lookup: {year_start}")
                return None

        if (league_id, year_start) in self._season_id_cache:
            return self._season_id_cache[(league_id, year_start)]

        query = "SELECT season_id FROM Seasons WHERE year_start = %s AND league_id = %s;"
        try:
            result = self.db_manager.execute_query(query, (year_start, league_id), fetch_mode='one')
            if result:
                season_id = result[0]
                self._season_id_cache[(league_id, year_start)] = season_id
                return season_id
            else:
                # Season not found, try to create it
                logger.info(f"Season not found for year {year_start}, league_id {league_id}. Attempting to create.")
                return self._ensure_season_exists(year_start, league_id)
        except Exception as e:
            logger.error(f"Error fetching season_id for year {year_start}, league_id {league_id}: {e}")
            return None

    def transform(self, extracted_data):
        """
        Transforms Wyatt O'Walsh's team history and current team snapshot data.
        
        Args:
            extracted_data (dict): A dictionary of DataFrames from WyattKaggleCSVExtractor,
                                   expected to contain 'wyatt_team_history' and 
                                   'wyatt_teams_current_snapshot'.
        
        Returns:
            dict: A dictionary containing two DataFrames:
                  'franchises_df' for public.franchises
                  'teams_df' for public.teams
                  Returns empty dict if essential data is missing.
        """
        df_history = extracted_data.get('wyatt_team_history')
        df_current_teams = extracted_data.get('wyatt_teams_current_snapshot')

        if df_history is None or df_history.empty:
            logger.error("Missing or empty 'wyatt_team_history' DataFrame.")
            return {}
        if df_current_teams is None or df_current_teams.empty:
            logger.error("Missing or empty 'wyatt_teams_current_snapshot' DataFrame.")
            return {}

        # Standardize column names for consistency
        df_history.columns = [str(col).lower().replace(' ', '_') for col in df_history.columns]
        df_current_teams.columns = [str(col).lower().replace(' ', '_') for col in df_current_teams.columns]

        franchises_data = []
        teams_data = []
        
        default_league_abbrev = 'NBA' 
        default_league_id = self._get_league_id(default_league_abbrev)
        if default_league_id is None:
            logger.error(f"Default league '{default_league_abbrev}' not found in database. Cannot proceed with Wyatt franchise/team transformation.")
            return {}

        source_franchise_ids = df_history['team_id'].unique()

        for src_franchise_id in source_franchise_ids:
            franchise_history_entries = df_history[df_history['team_id'] == src_franchise_id].sort_values(by='year_founded')
            
            if franchise_history_entries.empty:
                continue

            current_team_info = df_current_teams[df_current_teams['id'] == src_franchise_id]
            franchise_name_common = current_team_info['full_name'].iloc[0] if not current_team_info.empty else franchise_history_entries.iloc[-1]['nickname']

            first_year_founded = franchise_history_entries['year_founded'].min()
            last_year_active = franchise_history_entries['year_active_till'].max() 

            first_season_id = self._get_season_id(first_year_founded, default_league_id)
            last_season_id = self._get_season_id(last_year_active, default_league_id) if pd.notna(last_year_active) else None

            if first_season_id is None:
                 logger.warning(f"Could not find first_season_id for franchise {src_franchise_id} start year {first_year_founded}, league {default_league_abbrev}. Skipping franchise.")
                 continue
                 
            # Ensure first_season_id <= last_season_id to avoid violating the chk_franchise_season_order constraint
            if last_season_id is not None and first_season_id > last_season_id:
                logger.warning(f"Swapping first_season_id {first_season_id} and last_season_id {last_season_id} for franchise {src_franchise_id} to avoid constraint violation.")
                first_season_id, last_season_id = last_season_id, first_season_id

            franchises_data.append({
                'source_franchise_id': src_franchise_id, 
                'franchise_name_common': franchise_name_common,
                'first_season_id': first_season_id,
                'last_season_id': last_season_id,
                'notes': f"Source: Wyatt O'Walsh, Original ID: {src_franchise_id}"
            })

            for _, history_row in franchise_history_entries.iterrows():
                team_city = history_row['city']
                team_nickname = history_row['nickname']
                team_year_founded = int(history_row['year_founded'])
                team_year_active_till = int(history_row['year_active_till'])
                
                team_name_full = f"{team_city} {team_nickname}"
                current_abbrev = None
                current_state = None
                if not current_team_info.empty:
                    current_abbrev = current_team_info['abbreviation'].iloc[0]
                    if 'state' in current_team_info.columns:
                         current_state = current_team_info['state'].iloc[0]

                for year in range(team_year_founded, team_year_active_till + 1):
                    season_id_for_team_year = self._get_season_id(year, default_league_id)
                    if season_id_for_team_year is None:
                        logger.warning(f"Could not find season_id for team {team_name_full} year {year}, league {default_league_abbrev}. Skipping team-season.")
                        continue
                    
                    team_abbrev_for_season = current_abbrev # Simplification
                    state_province_for_season = current_state # Simplification
                                        
                    teams_data.append({
                        'source_franchise_id': src_franchise_id, 
                        'season_id': season_id_for_team_year,
                        'league_id': default_league_id,
                        'team_name_full': team_name_full,
                        'team_abbreviation': team_abbrev_for_season, 
                        'city': team_city,
                        'state_province': state_province_for_season, 
                        'country': 'USA', 
                        'notes': f"Source: Wyatt O'Walsh, Part of franchise ID: {src_franchise_id}"
                    })
        
        franchises_df = pd.DataFrame(franchises_data)
        teams_df = pd.DataFrame(teams_data)

        return {
            'franchises_df': franchises_df,
            'teams_df': teams_df
        }
