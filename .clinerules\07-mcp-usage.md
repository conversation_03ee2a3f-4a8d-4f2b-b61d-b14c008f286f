# MCP (Model Context Protocol) Usage Guidelines

This file outlines standards and best practices for interacting with the configured MCP servers (`context7-mcp`, `perplexity-mcp`, `sequentialthinking`, `github.com/modelcontextprotocol/servers/tree/main/src/postgres`, `github.com/zcaceres/markdownify-mcp`).

## 1. Purpose and Distinction

- **MCP for Tools:** Use the configured MCP servers primarily for interactions with **tools, data sources, and specific resources** that have structured inputs and outputs, as defined by each server's capabilities.
- **A2A for Agents:** Use the A2A protocol (if applicable in the project) for complex, stateful agent-to-agent collaboration where dynamic conversation and task management are needed. Choose the correct protocol for the interaction type.

## 2. Using Configured MCP Servers (Client-Side Code Generation)

- **Server & Tool Selection:**
    - **Identify the Goal:** Before calling an MCP tool, determine the precise sub-task needed (e.g., web search, library lookup, structured thinking).
    - **Select the Right Server:** Choose the most appropriate server based on its capabilities:
        - **`perplexity-mcp`**: Use for general web searches (`search`), conversational queries requiring external knowledge (`chat_perplexity`), finding relevant API documentation (`get_documentation`), locating specific APIs (`find_apis`), or checking for deprecated code usage (`check_deprecated_code`).
        - **`context7-mcp`**: Use specifically for resolving library identifiers (`resolve-library-id`) or retrieving documentation for known libraries (`get-library-docs`).
        - **`sequentialthinking`**: Use when a problem needs to be broken down into logical steps or requires a structured reasoning process (`sequentialthinking`).
        - **`github.com/modelcontextprotocol/servers/tree/main/src/postgres`**: Use for executing read-only SQL queries against the configured PostgreSQL database. Provides direct access to database resources (schemas).
        - **`github.com/zcaceres/markdownify-mcp`**: Use for converting various file formats (audio, DOCX, PDF, PPTX, XLSX, images, webpages, YouTube videos) to Markdown.
    - **Select the Right Tool:** Within the chosen server, select the specific tool (`search`, `resolve-library-id`, `sequentialthinking`, etc.) that best matches the sub-task. Refer to tool descriptions if available or inferred from context.
    - **Auto-Approval Note:** All listed tools in the current configuration are marked for auto-approval/always-allow, meaning Cline should proceed directly with the `call_tool` request without explicitly asking for user permission *for these specific tools*. However, always state *which* tool is being called.
- **Schema Adherence (Input):**
    - Code making `call_tool` requests MUST construct arguments precisely matching the target tool's **input schema** (assume standard schemas exist even if not explicitly defined here).
    - Implement client-side validation before sending the request where feasible, especially if input structure is complex or derived from user input.
- **Schema Adherence (Output):**
    - Code processing responses from `call_tool` MUST correctly parse the response, assuming it adheres to the tool's **output schema**.
    - Implement safe handling for potentially missing fields or unexpected data structures in the tool's response.
- **Error Handling:**
    - Generated client code MUST include robust error handling for MCP interactions.
    - Anticipate and handle potential errors from the MCP server (e.g., tool execution errors, network issues). Check for standard MCP error formats if applicable, or specific error messages returned by the tool.
    - Implement appropriate logging for failed MCP calls.
    - Consider adding retry logic (with backoff) only for clearly transient network errors, not for tool execution failures.
- **Authentication (Perplexity):**
    - **Requirement:** The `perplexity-mcp` server requires an API key (`PERPLEXITY_API_KEY`).
    - **CRITICAL:** Generated code that interacts with `perplexity-mcp` MUST retrieve this key from the environment variables (e.g., `os.environ.get("PERPLEXITY_API_KEY")` in Python, `process.env.PERPLEXITY_API_KEY` in Node.js).
    - **DO NOT** hardcode the API key directly in the generated source code. The calling environment is responsible for providing the key.
    - `context7-mcp` and `sequentialthinking` do not appear to require specific API keys based on the provided configuration.

- **Global Client Configuration Notes (e.g., `cline_mcp_settings.json`):**
    - When configuring MCP servers in global client settings (like `cline_mcp_settings.json` used by some MCP clients):
        - If a server requires dynamic inputs (such as database URLs or API keys) and the specific client's configuration format for `cline_mcp_settings.json` doesn't explicitly detail support for an `inputs` array for prompting (unlike how `.vscode/mcp.json` might work for VS Code specific inputs), it's often more reliable to provide concrete values directly in the `args` or `env` sections of the server's configuration. This is especially true if these values are known and can be securely managed (e.g., using environment variables that the MCP client itself can inject, or direct values for non-sensitive, fixed parameters).
        - Placeholders like `${input:variable_name}` (which are standard in VS Code's `.vscode/mcp.json` input system) might not be consistently or correctly substituted by all generic MCP client frontends when parsing `cline_mcp_settings.json`, or by all server startup scripts (especially those launched via wrappers like `npx`). If using such placeholders leads to connection or operational issues with a server, providing the direct value in the `args` or `env` should be a primary troubleshooting step. Always refer to the specific MCP client's documentation for how it handles `cline_mcp_settings.json` and variable substitution.

## 3. Generating Code for *New* MCP Tools/Servers (Server-Side - If Applicable)

*(Include this section if Cline might be asked to create new MCP tools/servers in this project)*

- **Tool Definition:** When generating code for a new MCP tool:
    - Use a clear, descriptive `name`.
    - Write a comprehensive `description` explaining purpose and limitations.
    - Define explicit **input and output schemas** (e.g., using Pydantic, TypeScript interfaces, JSON Schema).
    - Implement robust and efficient tool logic.
- **Server Implementation:**
    - Ensure correct tool registration.
    - Implement proper error handling within the tool to return meaningful MCP error responses.

## 4. General Best Practices

- **Idempotency:** When interacting with tools (especially those that might modify external state), consider if the operation is naturally idempotent or if the calling code needs to manage potential duplicate calls.
- **Clarity:** When planning to use an MCP tool, clearly state the server (`perplexity-mcp`, `context7-mcp`, `github.com/modelcontextprotocol/servers/tree/main/src/postgres`, `github.com/zcaceres/markdownify-mcp`, etc.) and the specific tool (`search`, `resolve-library-id`, `query`, `pdf-to-markdown`, etc.) that will be called in the implementation plan.
