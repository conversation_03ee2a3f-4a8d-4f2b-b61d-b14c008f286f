#!/usr/bin/env python3
# Expected location: c:\Users\<USER>\Projects\nbadb\Database\etl\extractors.py

import os
import logging
import pandas as pd
import sqlite3
import time
import json
from pathlib import Path
import requests
from datetime import datetime, timedelta
from typing import Optional, Tuple

# Configure logging
logger = logging.getLogger('etl_extractors')

class BaseExtractor:
    """Base class for all data extractors."""
    
    def __init__(self, config):
        """
        Initialize the extractor.
        
        Args:
            config (configparser.ConfigParser): Configuration object.
        """
        self.config = config
    
    def extract(self, *args, **kwargs):
        """
        Extract data from the source.
        
        Returns:
            dict: Dictionary of DataFrames, with keys as table names.
        """
        raise NotImplementedError("Subclasses must implement extract()")


class KaggleCSVExtractor(BaseExtractor):
    """Extractor for Kaggle CSV datasets."""
    
    def __init__(self, config, dataset_name):
        """
        Initialize the Kaggle CSV extractor.
        
        Args:
            config (configparser.ConfigParser): Configuration object.
            dataset_name (str): Name of the dataset (e.g., 'eoin_moore').
        """
        super().__init__(config)
        self.dataset_name = dataset_name
    
    def extract(self, csv_paths=None):
        """
        Extract data from CSV files.
        
        Args:
            csv_paths (dict, optional): Dictionary mapping CSV names to file paths.
                If None, use paths from config.
        
        Returns:
            dict: Dictionary of DataFrames, with keys as CSV names.
        """
        if csv_paths is None:
            csv_paths = {}
            # Get all config keys for this dataset
            prefix = f"{self.dataset_name}_"
            suffix = "_csv"
            for key in self.config['Kaggle']:
                if key.startswith(prefix) and key.endswith(suffix):
                    csv_name = key[len(prefix):-len(suffix)]
                    csv_paths[csv_name] = self.config['Kaggle'][key]
        
        dataframes = {}
        
        for csv_name, csv_path in csv_paths.items():
            if not os.path.exists(csv_path):
                logger.warning(f"CSV file not found: {csv_path}")
                continue
            
            logger.info(f"Reading CSV file: {csv_path}")
            try:
                df = pd.read_csv(csv_path)
                dataframes[csv_name] = df
                logger.info(f"Successfully read {len(df)} rows from {csv_path}")
            except Exception as e:
                logger.error(f"Error reading CSV file {csv_path}: {str(e)}")
        
        return dataframes


class KaggleSQLiteExtractor(BaseExtractor):
    """Extractor for Kaggle SQLite datasets."""
    
    def __init__(self, config, dataset_name):
        """
        Initialize the Kaggle SQLite extractor.
        
        Args:
            config (configparser.ConfigParser): Configuration object.
            dataset_name (str): Name of the dataset (e.g., 'wyatt_owalsh').
        """
        super().__init__(config)
        self.dataset_name = dataset_name
    
    def extract(self, sqlite_path=None, tables=None):
        """
        Extract data from SQLite database.
        
        Args:
            sqlite_path (str, optional): Path to SQLite database file.
                If None, use path from config.
            tables (list, optional): List of tables to extract.
                If None, extract all tables.
        
        Returns:
            dict: Dictionary of DataFrames, with keys as table names.
        """
        if sqlite_path is None:
            key = f"{self.dataset_name}_nba_database_sqlite"
            sqlite_path = self.config['Kaggle'][key]
        
        if not os.path.exists(sqlite_path):
            logger.warning(f"SQLite database not found: {sqlite_path}")
            return {}
        
        dataframes = {}
        
        try:
            # Connect to SQLite database
            conn = sqlite3.connect(sqlite_path)
            
            # Get list of tables
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            all_tables = [row[0] for row in cursor.fetchall()]
            
            # Filter tables if specified
            if tables is not None:
                tables_to_extract = [t for t in all_tables if t in tables]
            else:
                tables_to_extract = all_tables
            
            logger.info(f"Extracting {len(tables_to_extract)} tables from SQLite database: {sqlite_path}")
            
            # Extract each table
            for table in tables_to_extract:
                logger.info(f"Extracting table: {table}")
                try:
                    df = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                    dataframes[table] = df
                    logger.info(f"Successfully extracted {len(df)} rows from table {table}")
                except Exception as e:
                    logger.error(f"Error extracting table {table}: {str(e)}")
            
            conn.close()
        
        except Exception as e:
            logger.error(f"Error connecting to SQLite database {sqlite_path}: {str(e)}")
        
        return dataframes


class NBAAPIExtractor(BaseExtractor):
    """Extractor for NBA API data."""
    
    def __init__(self, config):
        """
        Initialize the NBA API extractor.
        
        Args:
            config (configparser.ConfigParser): Configuration object.
        """
        super().__init__(config)
        self.delay = int(self.config['API_NBA']['delay_seconds'])
        self.timeout = int(self.config['API_NBA']['timeout_seconds'])
        self.retries = int(self.config['API_NBA']['retries'])
    
    def extract(self, endpoints=None, seasons=None):
        """
        Extract data from NBA API.
        
        Args:
            endpoints (list, optional): List of endpoints to extract.
                If None, extract default endpoints.
            seasons (list, optional): List of seasons to extract (e.g., [2022, 2023]).
                If None, extract the most recent season.
        
        Returns:
            dict: Dictionary of DataFrames, with keys as endpoint names.
        """
        try:
            from nba_api.stats.endpoints import commonallplayers, leaguedashteamstats, leaguegamefinder
        except ImportError:
            logger.error("NBA API package not installed. Install with: pip install nba-api")
            return {}
        
        if endpoints is None:
            endpoints = ['players', 'teams', 'games']
        
        if seasons is None:
            # Default to current season
            current_year = datetime.now().year
            current_month = datetime.now().month
            # NBA seasons span two years and typically start in October
            if current_month >= 10:  # October or later
                seasons = [current_year + 1]  # e.g., 2023-24 season
            else:
                seasons = [current_year]  # e.g., 2022-23 season
        
        dataframes = {}
        
        for endpoint in endpoints:
            logger.info(f"Extracting data from NBA API endpoint: {endpoint}")
            
            for season in seasons:
                try:
                    if endpoint == 'players':
                        # Get all players
                        players = commonallplayers.CommonAllPlayers(season=season)
                        df = players.get_data_frames()[0]
                        key = f"players_{season}"
                    
                    elif endpoint == 'teams':
                        # Get team stats
                        teams = leaguedashteamstats.LeagueDashTeamStats(season=f"{season-1}-{str(season)[-2:]}")
                        df = teams.get_data_frames()[0]
                        key = f"teams_{season}"
                    
                    elif endpoint == 'games':
                        # Get game data
                        games = leaguegamefinder.LeagueGameFinder(season_nullable=f"{season-1}-{str(season)[-2:]}")
                        df = games.get_data_frames()[0]
                        key = f"games_{season}"
                    
                    else:
                        logger.warning(f"Unknown NBA API endpoint: {endpoint}")
                        continue
                    
                    dataframes[key] = df
                    logger.info(f"Successfully extracted {len(df)} rows from NBA API endpoint {endpoint} for season {season}")
                    
                    # Add delay between API calls
                    time.sleep(self.delay)
                
                except Exception as e:
                    logger.error(f"Error extracting data from NBA API endpoint {endpoint} for season {season}: {str(e)}")
        
        return dataframes


class BasketballReferenceExtractor(BaseExtractor):
    """Extractor for Basketball Reference data."""
    
    def __init__(self, config):
        """
        Initialize the Basketball Reference extractor.
        
        Args:
            config (configparser.ConfigParser): Configuration object.
        """
        super().__init__(config)
        self.delay = int(self.config['BasketballReference']['delay_seconds'])
        self.timeout = int(self.config['BasketballReference']['timeout_seconds'])
        self.retries = int(self.config['BasketballReference']['retries'])
    
    def extract(self, data_type: str, season: Optional[int] = None, date_range: Optional[Tuple[datetime, datetime]] = None, **kwargs):
        """
        Extract data from Basketball Reference.
        
        Args:
            data_type (str): Type of data to extract.
            season (int, optional): Season to extract (e.g., 2022).
            date_range (tuple, optional): Tuple of (start_date, end_date) for game data.
                If None, use a default range.
        
        Returns:
            dict: Dictionary of DataFrames, with keys as data type names.
        """
        try:
            from basketball_reference_web_scraper import client
        except ImportError:
            logger.error("Basketball Reference scraper not installed. Install with: pip install basketball-reference-web-scraper")
            return {}
        
        if data_type == 'players':
            # Get players for the season
            players = client.players_season_totals(season_end_year=season)
            df = pd.DataFrame(players)
            key = f"players_{season}"
        
        elif data_type == 'teams':
            # For teams, we'll extract from the standings
            # This is a simplified approach - you may need to adjust based on the actual API
            standings = client.standings(season_end_year=season)
            df = pd.DataFrame(standings)
            key = f"teams_{season}"
        
        elif data_type == 'games':
            if not season and not date_range:
                logger.error("For 'games' data_type, either 'season' or 'date_range' must be provided.")
                return pd.DataFrame()

            games_data = []
            # Determine season_end_year. If date_range is provided, derive from the end date.
            # This assumes games for a season X-(X+1) are primarily in calendar year X+1 for season_schedule.
            # Or, if only season (e.g., 2022 for 2022-23) is given, it's season_end_year.
            effective_season_end_year = season
            if date_range:
                # If date_range spans Dec-Jan, use the year of the later date for season_schedule
                # e.g. Dec 2022 - Jan 2023 range, season_schedule needs 2023
                if date_range[0].month == 12 and date_range[1].month == 1:
                    effective_season_end_year = date_range[1].year
                else: # Otherwise, assume the year of the start_date is sufficient or use provided season
                    effective_season_end_year = date_range[0].year + 1 if not season else season
            
            if not effective_season_end_year:
                logger.error("Could not determine effective_season_end_year for fetching games.")
                return pd.DataFrame()

            logger.info(f"Fetching Basketball Reference games for season ending {effective_season_end_year}")
            try:
                season_games_list = client.season_schedule(season_end_year=effective_season_end_year)
                if season_games_list:
                    df_season_games = pd.DataFrame(season_games_list)
                    if 'start_time' in df_season_games.columns and not df_season_games.empty:
                        df_season_games['start_time'] = pd.to_datetime(df_season_games['start_time'])

                        if date_range:
                            # Ensure date_range components are datetime and timezone-aware (or naive) consistently
                            # Assuming start_time from bball_ref is naive UTC, make date_range naive too for comparison
                            start_dt = pd.to_datetime(date_range[0]).tz_localize(None)
                            end_dt = pd.to_datetime(date_range[1]).tz_localize(None)
                            df_season_games['start_time'] = df_season_games['start_time'].dt.tz_localize(None)

                            filtered_df = df_season_games[
                                (df_season_games['start_time'] >= start_dt) &
                                (df_season_games['start_time'] <= end_dt)
                            ]
                            games_data = filtered_df.to_dict('records')
                            logger.info(f"Filtered {len(games_data)} games for date range {date_range[0].strftime('%Y-%m-%d')} to {date_range[1].strftime('%Y-%m-%d')}.")
                        else:
                            # If no date_range, but season was given, return all games for that season
                            games_data = df_season_games.to_dict('records')
                            logger.info(f"Fetched {len(games_data)} games for season ending {effective_season_end_year}.")
                    else:
                        logger.warning(f"No 'start_time' column or empty data from season_schedule for {effective_season_end_year}.")
                else:
                    logger.warning(f"No games returned from client.season_schedule for {effective_season_end_year}.")
            except Exception as e:
                logger.error(f"Error fetching/processing Basketball Reference games for season {effective_season_end_year}: {e}")
                # Fallback to empty DataFrame on error

            df = pd.DataFrame(games_data)
            # Construct a more robust key
            if date_range:
                key_suffix = f"{date_range[0].strftime('%Y%m%d')}_{date_range[1].strftime('%Y%m%d')}"
            elif season:
                key_suffix = str(season)
            else:
                key_suffix = "all_time" # Should not happen due to checks above
            key = f"games_{key_suffix}"
        
        else:
            logger.warning(f"Unknown Basketball Reference data type: {data_type}")
            return {}
        
        if df.empty:
            logger.warning(f"No data extracted for {data_type} from Basketball Reference for season {season}")
            return {}
        
        dataframes = {key: df}
        logger.info(f"Successfully extracted {len(df)} rows of {data_type} data from Basketball Reference for season {season}")
        
        # Add delay between requests
        time.sleep(self.delay)
        
        return dataframes


class WyattKaggleCSVExtractor(BaseExtractor):
    """Extractor for Wyatt O'Walsh's Kaggle CSV datasets."""

    def __init__(self, config):
        """
        Initialize the Wyatt O'Walsh Kaggle CSV extractor.
        
        Args:
            config (configparser.ConfigParser): Configuration object.
        """
        super().__init__(config)
        data_dir_value = self.config['General']['data_directory']
        # Clean the path: take everything before a potential inline comment character ';'
        cleaned_data_dir = data_dir_value.split(';')[0].strip()
        self.base_path = Path(cleaned_data_dir) / 'kaggle_datasets' / 'wyatt' / 'csv'

    def extract_team_history(self):
        """
        Extracts team history data (franchise evolution).
        Expected file: team_history.csv
        
        Returns:
            pandas.DataFrame: DataFrame containing team history data, or None if an error occurs.
        """
        file_path = self.base_path / 'team_history.csv'
        if not file_path.exists():
            logger.warning(f"Wyatt O'Walsh team_history.csv not found at: {file_path}")
            return None
        
        logger.info(f"Reading Wyatt O'Walsh team_history.csv from: {file_path}")
        try:
            df = pd.read_csv(file_path)
            logger.info(f"Successfully read {len(df)} rows from {file_path.name}")
            return df
        except Exception as e:
            logger.error(f"Error reading {file_path.name}: {str(e)}")
            return None

    def extract_teams_current_snapshot(self):
        """
        Extracts current team snapshot data (details of current teams).
        Expected file: team.csv (or could be team_details.csv if preferred)
        
        Returns:
            pandas.DataFrame: DataFrame containing current team data, or None if an error occurs.
        """
        # We'll use team.csv as it's simpler and has the core info for franchise common name.
        # team_details.csv has more fields that can be used later for enriching the 'teams' table.
        file_path = self.base_path / 'team.csv'
        if not file_path.exists():
            logger.warning(f"Wyatt O'Walsh team.csv not found at: {file_path}")
            return None
        
        logger.info(f"Reading Wyatt O'Walsh team.csv from: {file_path}")
        try:
            df = pd.read_csv(file_path)
            logger.info(f"Successfully read {len(df)} rows from {file_path.name}")
            return df
        except Exception as e:
            logger.error(f"Error reading {file_path.name}: {str(e)}")
            return None

    def extract(self, data_types=None):
        """
        Main extraction method for Wyatt O'Walsh CSV data.
        
        Args:
            data_types (list, optional): List of data types to extract. 
                                         Supported: ['team_history', 'teams_current_snapshot'].
                                         If None, extracts all supported types.
        
        Returns:
            dict: Dictionary of DataFrames, with keys as data type names.
        """
        if data_types is None:
            data_types = ['team_history', 'teams_current_snapshot']

        dataframes = {}

        if 'team_history' in data_types:
            df_team_history = self.extract_team_history()
            if df_team_history is not None:
                dataframes['wyatt_team_history'] = df_team_history
        
        if 'teams_current_snapshot' in data_types:
            df_teams_current = self.extract_teams_current_snapshot()
            if df_teams_current is not None:
                dataframes['wyatt_teams_current_snapshot'] = df_teams_current
        
        # Add more specific extractors here as needed, e.g., for players, games
        
        return dataframes
