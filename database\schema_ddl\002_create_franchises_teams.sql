-- DDL for Franchises and Teams Tables

BEGIN;

-- Table: Franchises
-- Purpose: Stores information about historical NBA franchises, allowing tracking of team lineage across seasons and relocations.
CREATE TABLE IF NOT EXISTS public.franchises (
    franchise_id SERIAL PRIMARY KEY,
    franchise_name_common TEXT NOT NULL UNIQUE,
    first_season_id INTEGER REFERENCES public.seasons(season_id) ON DELETE SET NULL,
    last_season_id INTEGER REFERENCES public.seasons(season_id) ON DELETE SET NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT chk_franchise_season_order CHECK (first_season_id IS NULL OR last_season_id IS NULL OR first_season_id <= last_season_id)
);

COMMENT ON TABLE public.franchises IS 'Stores information about historical NBA franchises, allowing tracking of team lineage across seasons and relocations.';
COMMENT ON COLUMN public.franchises.franchise_id IS 'Unique identifier for the franchise.';
COMMENT ON COLUMN public.franchises.franchise_name_common IS 'Common name of the franchise (e.g., Lakers, Celtics). Used to group teams that are part of the same historical lineage.';
COMMENT ON COLUMN public.franchises.first_season_id IS 'Foreign key to the first season this franchise participated in.';
COMMENT ON COLUMN public.franchises.last_season_id IS 'Foreign key to the last season this franchise participated in (NULL if still active).';
COMMENT ON COLUMN public.franchises.notes IS 'Miscellaneous notes about the franchise.';
COMMENT ON COLUMN public.franchises.created_at IS 'Timestamp of when the record was created.';
COMMENT ON COLUMN public.franchises.updated_at IS 'Timestamp of when the record was last updated.';

-- Table: Teams
-- Purpose: Stores information about specific team instances within a given season and league, linked to a franchise.
CREATE TABLE IF NOT EXISTS public.teams (
    team_id SERIAL PRIMARY KEY,
    franchise_id INTEGER NOT NULL REFERENCES public.franchises(franchise_id) ON DELETE RESTRICT,
    season_id INTEGER NOT NULL REFERENCES public.seasons(season_id) ON DELETE CASCADE,
    league_id INTEGER NOT NULL REFERENCES public.leagues(league_id) ON DELETE RESTRICT,
    team_name_full TEXT NOT NULL,
    team_abbreviation VARCHAR(10) NOT NULL,
    city TEXT NOT NULL,
    state_province TEXT,
    country TEXT NOT NULL,
    arena_name TEXT,
    arena_capacity INTEGER,
    owner_name TEXT,
    general_manager_name TEXT,
    head_coach_name TEXT,
    d_league_affiliation_name TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT uq_team_franchise_season UNIQUE (franchise_id, season_id),
    CONSTRAINT uq_team_league_season_name UNIQUE (league_id, season_id, team_name_full),
    CONSTRAINT uq_team_league_season_abbreviation UNIQUE (league_id, season_id, team_abbreviation)
);

COMMENT ON TABLE public.teams IS 'Stores information about specific team instances within a given season and league, linked to a franchise.';
COMMENT ON COLUMN public.teams.team_id IS 'Unique identifier for the team season record.';
COMMENT ON COLUMN public.teams.franchise_id IS 'Foreign key linking to the historical franchise this team instance belongs to.';
COMMENT ON COLUMN public.teams.season_id IS 'Foreign key linking to the specific season this team instance played in.';
COMMENT ON COLUMN public.teams.league_id IS 'Foreign key linking to the league this team instance participated in.';
COMMENT ON COLUMN public.teams.team_name_full IS 'Full official name of the team for that season (e.g., Los Angeles Lakers, Boston Celtics 1985-86).';
COMMENT ON COLUMN public.teams.team_abbreviation IS 'Official abbreviation for the team in that season (e.g., LAL, BOS).';
COMMENT ON COLUMN public.teams.city IS 'City where the team was based for that season.';
COMMENT ON COLUMN public.teams.state_province IS 'State or province where the team was based (if applicable).';
COMMENT ON COLUMN public.teams.country IS 'Country where the team was based.';
COMMENT ON COLUMN public.teams.arena_name IS 'Name of the primary arena the team played in during that season.';
COMMENT ON COLUMN public.teams.arena_capacity IS 'Seating capacity of the primary arena.';
COMMENT ON COLUMN public.teams.owner_name IS 'Name of the team owner.';
COMMENT ON COLUMN public.teams.general_manager_name IS 'Name of the team general manager.';
COMMENT ON COLUMN public.teams.head_coach_name IS 'Name of the team head coach.';
COMMENT ON COLUMN public.teams.d_league_affiliation_name IS 'Name of the team D-League affiliation.';
COMMENT ON COLUMN public.teams.notes IS 'Miscellaneous notes about this specific team instance.';
COMMENT ON COLUMN public.teams.created_at IS 'Timestamp of when the record was created.';
COMMENT ON COLUMN public.teams.updated_at IS 'Timestamp of when the record was last updated.';

-- Trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for franchises table
CREATE OR REPLACE TRIGGER set_franchises_updated_at
BEFORE UPDATE ON public.franchises
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Triggers for teams table
CREATE OR REPLACE TRIGGER set_teams_updated_at
BEFORE UPDATE ON public.teams
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

COMMIT;
